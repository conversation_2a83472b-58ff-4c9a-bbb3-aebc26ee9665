/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: PictureMarkDelegate
 * Description:
 * Version: 1.0
 * Date: 2022/10/24
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2022/10/24 1.0 create
 */

package com.soundrecorder.wavemark.picturemark

import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.view.View
import androidx.activity.result.ActivityResultLauncher
import androidx.annotation.StringRes
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStoreOwner
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.poplist.PopupListItem
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.dismissWhenShowing
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.databean.MarkMetaData
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.utils.EnableAppUtil
import com.soundrecorder.common.utils.MarkSerializUtil
import com.soundrecorder.imageload.ImageLoaderUtils
import com.soundrecorder.imageload.utils.ImageUtils.uri2File
import com.soundrecorder.modulerouter.waveMark.IIPictureMarkListener
import com.soundrecorder.modulerouter.waveMark.IPictureMarkDelegate
import com.soundrecorder.modulerouter.waveMark.IPictureMarkLifeOwnerProvider
import com.soundrecorder.wavemark.R
import com.soundrecorder.wavemark.mark.MarkHelper
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.Dispatchers.Main
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class PictureMarkDelegate(
    private val iOwnerProvider: IPictureMarkLifeOwnerProvider,
    var hasActivityReCreate: Boolean,
    private val mIIPictureMarkListener: IIPictureMarkListener<MarkMetaData, MarkDataBean>?,
) : IPictureMarkDelegate<MarkMetaData>, DefaultLifecycleObserver {
    companion object {
        private const val CURRENT_TIME_MILLIS_KEY = "current_time_millis_key"
        private const val POSITION_0 = 0
        private const val POSITION_1 = 1
    }

    private val mLogLog = "PictureMarkDelegate"

    private var mSelectPictureDialog: COUIPopupListWindow? = null
    private var appDisableDialog: AlertDialog? = null

    private val viewModel by lazy {
        ViewModelProvider(iOwnerProvider.provideLifeCycleOwner() as ViewModelStoreOwner)[PictureMarkBaseActivityViewModel::class.java]
    }

    private var takePicture: ActivityResultLauncher<Unit>? = null

    private var takePhotoAlbum: ActivityResultLauncher<Unit>? = null

    private var currentTimeMillis: Long = -1L
    private var takeCamera: TakeCamera? = null

    init {
        iOwnerProvider.provideLifeCycleOwner().lifecycleScope.launchWhenCreated {
            iOwnerProvider.provideLifeCycleOwner().lifecycle.addObserver(this@PictureMarkDelegate)
        }
    }

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        mIIPictureMarkListener?.supportPictureMarkSource()?.apply {
            contains(IPictureMarkDelegate.SOURCE_CAMERA).apply {
                initTakePicture()
            }
            contains(IPictureMarkDelegate.SOURCE_ALBUM).apply {
                initTakePhotoAlbum()
            }
        }
    }

    override fun onResume(owner: LifecycleOwner) {
        viewModel.requestCodeX = -1
        hasActivityReCreate = false
    }

    override fun onDestroy(owner: LifecycleOwner) {
        owner.lifecycle.removeObserver(this)
        val isFinishing = iOwnerProvider.isFinishing()
        if (isFinishing) {
            clearActivityWithRequestCode()
            ImageLoaderUtils.clearMemoryCache()
            takePicture?.unregister()
            takePhotoAlbum?.unregister()
        }
        mIIPictureMarkListener?.releaseMarks(isFinishing)
        releaseDialog()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        outState.putLong(CURRENT_TIME_MILLIS_KEY, currentTimeMillis)
    }

    /**使用此方式避免拍照/选择照片过程中，去关闭录音权限，再回到该页面选择照片后回来，标记添加到第0秒*/
    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        this.currentTimeMillis = savedInstanceState.getLong(CURRENT_TIME_MILLIS_KEY, -1)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        appDisableDialog.dismissWhenShowing()
        appDisableDialog = null
    }

    override fun onNewIntent(intent: Intent?) {
        DebugUtil.i(mLogLog, "onNewIntent")
        if (viewModel.isAddPictureMarking.value == true && mSelectPictureDialog?.isShowing != true) {
            //需要移除requestCode相关页面
            clearActivityWithRequestCode()
            resetAddPictureMarking()
            restorePlayerStatePlaying()
        }
    }

    /**通过requestCode关闭启动的相机界面，相册界面，相册预览*/
    private fun clearActivityWithRequestCode() {
        if (viewModel.requestCodeX != -1) {
            iOwnerProvider.provideActivity().finishActivity(viewModel.requestCodeX)
            viewModel.requestCodeX = -1
        }
    }

    private fun releaseDialog() {
        mSelectPictureDialog?.dismiss()
        mSelectPictureDialog = null
        appDisableDialog.dismissWhenShowing()
        appDisableDialog = null
    }

    override fun launchTakePhotoSingle(sourceType: Int, timeMill: Long): Boolean {
        return when (sourceType) {
            IPictureMarkDelegate.SOURCE_ALBUM -> launchTackPhotoAlbum(timeMill)
            IPictureMarkDelegate.SOURCE_CAMERA -> launchTakePicture(timeMill)
            else -> false
        }
    }

    override fun postShowSelectPictureDialog(anchor: View?) {
        showSelectPictureDialog(anchor)
        viewModel.pictureMarkTime = mIIPictureMarkListener?.getPlayerCurrentTimeMillis() ?: -1
    }

    override fun getRequestCodeX(): Int {
        return viewModel.requestCodeX
    }

    override fun setRequestCodeX(requestCode: Int) {
        DebugUtil.i(mLogLog, "setRequestCodeX  $requestCode")
        viewModel.requestCodeX = requestCode
    }

    override fun setIsAddPictureMarking(isAddPictureMarking: Boolean) {
        viewModel.isAddPictureMarking.postValueSafe(isAddPictureMarking)
    }

    override fun isAddPictureMarking(): MutableLiveData<Boolean> = viewModel.isAddPictureMarking

    override fun checkNeedAddMark(): Boolean {
        return when {
            checkAddMarkMoreThanMax() -> {
                DebugUtil.d(mLogLog, "checkNeedAddMark: checkAddMarkMoreThanMax")
                ToastManager.showShortToast(BaseApplication.getAppContext(),
                    BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.photo_mark_recommend_mark_limit))
                false
            }
            checkAddMarkDuplicated(mIIPictureMarkListener?.getPlayerCurrentTimeMillis() ?: -1) -> {
                DebugUtil.d(mLogLog, "checkNeedAddMark: checkAddMarkDuplicated curTime is ${mIIPictureMarkListener?.getPlayerCurrentTimeMillis()}")
                false
            }
            checkAddMarkWhenCompleted(mIIPictureMarkListener?.getPlayerCurrentTimeMillis() ?: -1) -> {
                DebugUtil.d(mLogLog, "checkNeedAddMark: checkAddMarkWhenCompleted curTime is ${mIIPictureMarkListener?.getPlayerCurrentTimeMillis()}")
                false
            }
            else -> true
        }
    }

    override fun checkAddMarkMoreThanMax(): Boolean = (mIIPictureMarkListener?.getMarkList()?.size ?: 0) >= MarkHelper.MAX_MARKER_COUNT

    override fun checkAddMarkDuplicated(mill: Long): Boolean {
        return MarkSerializUtil.checkInTimeScopeInMarkList(mIIPictureMarkListener?.getMarkList() as? MutableList<MarkDataBean>, mill) != -1
    }

    private fun checkAddMarkWhenCompleted(currentTimeMillis: Long): Boolean = currentTimeMillis == (mIIPictureMarkListener?.getDuration() ?: -1)

    private fun setAddPictureMarking() {
        viewModel.isAddPictureMarking.postValueSafe(true)
    }

    fun resetAddPictureMarking() {
        viewModel.isAddPictureMarking.postValueSafe(false)
    }

    /**
     * 图片标记-相册
     */
    private fun initTakePhotoAlbum() {
        if (takePhotoAlbum != null) {
            return
        }
        val launchOwnerFragment = iOwnerProvider.provideLifeCycleOwner() as? Fragment
        val launchOwnerActivity = iOwnerProvider.provideLifeCycleOwner() as? AppCompatActivity
        takePhotoAlbum = (launchOwnerFragment ?: launchOwnerActivity)?.registerForActivityResult(TakePhotoAlbum()) { result ->
            if (result != null) {
                viewModel.viewModelScope.launch(IO) {
                    val file = FileUtils.getAppFile()
                    val imageParceResult = result.uri2File(file)
                    if (imageParceResult != null) {
                        withContext(Main) {
                            val markMetaData = MarkMetaData(
                                "",
                                file.name,
                                currentTimeMillis,
                                imageParceResult.width,
                                imageParceResult.height
                            )
                            mIIPictureMarkListener?.doPictureMark(markMetaData)
                        }
                    } else {
                        ToastManager.showLongToast(
                            BaseApplication.getAppContext(),
                            R.string.error_picture_mark_content
                        )
                    }
                }
                mIIPictureMarkListener?.doSingleOrMultiPictureMarkEnd(false, IPictureMarkDelegate.SOURCE_ALBUM)
            } else {
                mIIPictureMarkListener?.doSingleOrMultiPictureMarkEnd(true, IPictureMarkDelegate.SOURCE_ALBUM)
            }
            resetAddPictureMarking()
            restorePlayerStatePlaying()
        }
    }

    /**
     * 图片标记-相机
     */
    private fun initTakePicture() {
        if (takePicture != null) return
        val launchOwnerFragment = iOwnerProvider.provideLifeCycleOwner() as? Fragment
        val launchOwnerActivity = iOwnerProvider.provideLifeCycleOwner() as? AppCompatActivity
        takeCamera = TakeCamera()
        takePicture = (launchOwnerFragment ?: launchOwnerActivity)?.registerForActivityResult(takeCamera!!) { result ->
            if (result != null) {
                viewModel.viewModelScope.launch(IO) {
                    val file = FileUtils.getAppFile()
                    val imageParseResult = result.uri2File(file)
                    if (imageParseResult != null) {
                        withContext(Main) {
                            val markMetaData = MarkMetaData(
                                "",
                                file.name,
                                currentTimeMillis,
                                imageParseResult.width,
                                imageParseResult.height
                            )
                            mIIPictureMarkListener?.doPictureMark(markMetaData)
                        }
                    } else {
                        ToastManager.showLongToast(
                            BaseApplication.getAppContext(),
                            R.string.error_picture_mark_content
                        )
                    }
                }
                mIIPictureMarkListener?.doSingleOrMultiPictureMarkEnd(false, IPictureMarkDelegate.SOURCE_CAMERA)
            } else {
                mIIPictureMarkListener?.doSingleOrMultiPictureMarkEnd(true, IPictureMarkDelegate.SOURCE_CAMERA)
            }
            resetAddPictureMarking()
            restorePlayerStatePlaying()
        }
    }

    private fun showEnableDialog(packageName: String, @StringRes title: Int, @StringRes content: Int) {
        appDisableDialog.dismissWhenShowing()
        appDisableDialog = EnableAppUtil.showEnableDialog(
            iOwnerProvider.provideActivity(),
            packageName,
            title,
            content) {
            resetAddPictureMarking()
            restorePlayerStatePlaying()
        }
    }

    override fun getSelectMarkTime(): Long? {
        return viewModel.markTime
    }

    override fun saveSelectMarkTime() {
        viewModel.markTime = mIIPictureMarkListener?.getPlayerCurrentTimeMillis() ?: -1
    }

    override fun handleSelectPictureMark(position: Int) {
        setAddPictureMarking()
        savePlayerStatePlaying()
        viewModel.pictureMarkTime = viewModel.markTime
        when (position) {
            POSITION_0 -> {
                if (!launchTakePicture(viewModel.pictureMarkTime)) {
                    resetAddPictureMarking()
                }
                BuryingPoint.playingAddPictureByCamera()
            }

            POSITION_1 -> {
                if (!launchTackPhotoAlbum(viewModel.pictureMarkTime)) {
                    resetAddPictureMarking()
                }
                BuryingPoint.playingAddPictureByAlbum()
            }
        }
    }

    override fun dismissPictureMark() {
        resetAddPictureMarking()
        restorePlayerStatePlaying(false)
        BuryingPoint.playingAddPictureDialogNotOnClickNumber()
    }

    private fun showSelectPictureDialog(anchor: View?) {
        if (anchor == null) {
            DebugUtil.e(mLogLog, "showSelectPictureDialog: anchor null")
            return
        }
        setAddPictureMarking()
        savePlayerStatePlaying()
        var select = false
        mSelectPictureDialog?.dismiss()
        DebugUtil.d(mLogLog, "showSelectPictureDialog: init dialog")

        mSelectPictureDialog = COUIPopupListWindow(iOwnerProvider.provideActivity()).apply {
            itemList = mutableListOf<PopupListItem>().apply {
                add(PopupListItem(BaseApplication.getAppContext().getString(R.string.take_photo), true))
                add(PopupListItem(BaseApplication.getAppContext().getString(R.string.use_album), true))
            }
            setDismissTouchOutside(true)
            setOnDismissListener {
                if (mSelectPictureDialog != null) {
                    if (!select) {
                        resetAddPictureMarking()
                        restorePlayerStatePlaying(false)
                        BuryingPoint.playingAddPictureDialogNotOnClickNumber()
                    }
                }
            }
        }
        mSelectPictureDialog?.setOnItemClickListener { _, _, position, _ ->
            when (position) {
                0 -> {
                    if (!launchTakePicture(viewModel.pictureMarkTime)) {
                        resetAddPictureMarking()
                    }
                    select = true
                    BuryingPoint.playingAddPictureByCamera()
                }
                1 -> {
                    if (!launchTackPhotoAlbum(viewModel.pictureMarkTime)) {
                        resetAddPictureMarking()
                    }
                    select = true
                    BuryingPoint.playingAddPictureByAlbum()
                }
            }
            mSelectPictureDialog?.dismiss()
        }
        mSelectPictureDialog?.showAtAboveOrBelow(anchor)
    }

    private fun launchTakePicture(currentTimeMillis: Long): Boolean {
        val appInfo = TakeCamera.getCameraPackageName()
        if (appInfo.first.isNullOrEmpty()) {
            return false
        }
        takeCamera?.cameraPackageName = appInfo.first
        if (appInfo.second == EnableAppUtil.APP_IS_DISABLED) {
            showEnableDialog(
                appInfo.first!!,
                R.string.is_open_camera,
                R.string.camera_disable_content_v3
            )
            return false
        }
        this.currentTimeMillis = currentTimeMillis
        try {
            return takePicture?.let {
                it.launch(Unit)
                mIIPictureMarkListener?.onActivityLaunched(IPictureMarkDelegate.SOURCE_CAMERA)
                return true
            } ?: false
        } catch (ignored: Exception) {
            DebugUtil.e(mLogLog, "lauch error", ignored)
        }
        return false
    }

    private fun launchTackPhotoAlbum(currentTimeMillis: Long): Boolean {
        val packageName = TakePhotoAlbum.getAlumPackageName()
        val enableResult = EnableAppUtil.isAppInstallEnabled(iOwnerProvider.provideActivity().applicationContext, packageName)

        if (enableResult == EnableAppUtil.APP_NOT_INSTALLED) {
            return false
        }
        if (enableResult == EnableAppUtil.APP_IS_DISABLED) {
            showEnableDialog(
                packageName,
                R.string.is_open_gallery,
                R.string.album_disable_content_v3
            )
            return false
        }
        this.currentTimeMillis = currentTimeMillis
        try {
            return takePhotoAlbum?.let {
                it.launch(Unit)
                mIIPictureMarkListener?.onActivityLaunched(IPictureMarkDelegate.SOURCE_ALBUM)
                return true
            } ?: false
        } catch (ignored: Exception) {
            DebugUtil.e(mLogLog, "lauch error", ignored)
        }
        return false
    }

    private fun savePlayerStatePlaying() {
        if (hasActivityReCreate) {
            return
        }
        viewModel.beforeShowDialogHasPlayStatus = mIIPictureMarkListener?.hasPlayerStatePlaying() ?: -1
        mIIPictureMarkListener?.beforeShowSelectPictureDialog()
        DebugUtil.d(mLogLog, "savePlayerStatePlaying: pausePlayer end")
    }

    private fun restorePlayerStatePlaying(hasNeedSpeakOff: Boolean = true) {
        viewModel.beforeShowDialogHasPlayStatus =
            mIIPictureMarkListener?.restorePlayerStatePlaying(viewModel.beforeShowDialogHasPlayStatus, hasNeedSpeakOff) ?: -1
    }

    override fun getViewModel(): ViewModel = viewModel

    override fun isDialogShowing(): Boolean {
        return mSelectPictureDialog?.isShowing() ?: false
    }
}