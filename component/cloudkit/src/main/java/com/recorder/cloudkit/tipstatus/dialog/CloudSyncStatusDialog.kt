package com.recorder.cloudkit.tipstatus.dialog

import android.content.Context
import android.content.res.Configuration
import android.os.BatteryManager
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.LifecycleOwner
import com.coui.appcompat.button.COUIButton
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.recorder.cloudkit.R
import com.recorder.cloudkit.SyncTriggerManager
import com.recorder.cloudkit.sync.CloudSyncAgent
import com.recorder.cloudkit.sync.RecordSyncChecker
import com.recorder.cloudkit.sync.bean.CloudSyncResult
import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode.*
import com.recorder.cloudkit.sync.listener.NotifyDialogListener
import com.recorder.cloudkit.tipstatus.TipStatusManager
import com.soundrecorder.modulerouter.cloudkit.dialog.ICloudSyncStatusDialog

class CloudSyncStatusDialog(private val mContext: Context, private var owner: LifecycleOwner?) :
    View.OnClickListener, NotifyDialogListener, ICloudSyncStatusDialog {

    companion object {
        private const val TAG = "CloudSyncStatusDialog"
        private const val RETRY_DELAY_TIME = 3500L
        internal const val MESSAGE_REFRESH_UI = 0
    }

    var mDialog: COUIBottomSheetDialog? = null
    var mFragmentManager: FragmentManager? = null
    private var mButton: COUIButton? = null
    private var mImageView: ImageView? = null
    private var mTextView: TextView? = null

    private val mCallback: (Int, Int, Int, String?) -> Unit = { imageResId, contentResId, buttonResId, stringFormat ->
        setTextColorAndText(imageResId, contentResId, stringFormat)
        setButtonText(buttonResId)
    }

    private val mHandler: Handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            if (msg.what == MESSAGE_REFRESH_UI) {
                mButton?.isEnabled = true
                accordingStateShowImage()
            }
        }
    }

    init {
        val view = View.inflate(mContext, R.layout.dialog_cloud_sync_status_layout, null)
        mButton = view.findViewById(R.id.dialog_action)
        mImageView = view.findViewById(R.id.dialog_img)
        mTextView = view.findViewById(R.id.dialog_des)
        mButton?.setOnClickListener(this)
        mDialog = COUIBottomSheetDialog(mContext, com.support.panel.R.style.DefaultBottomSheetDialog)
        mDialog?.contentView = view
        mDialog?.setOnDismissListener {
            TipStatusManager.releaseNotifyDialogListener()
            resetRetry()
        }
    }

    /**
     * 如果需要在回到页面时刷新状态并同步，调用此方法
     * 设置省电模式
     * 清理内存
     */
    override fun onResume() {
        when (getCloudState()) {
            RESULT_POWER_SAVING_MODE -> {
                if (RecordSyncChecker.isPowerSaveMode(mContext)) {
                    return
                }
                //回来省电模式关闭的情况下
                //如果当前正在充电  电量大于10自动同步   如果小于10 直接流转状态低电量充电中
                //如果回来未处于充电状态       小于20 直接流转状态到低电量
                val batteryInfo = RecordSyncChecker.getBatteryInfo(BaseApplication.getAppContext())
                if (batteryInfo != null) {
                    val isCharging: Boolean =
                        batteryInfo.chargeStatus == BatteryManager.BATTERY_STATUS_CHARGING
                                || batteryInfo.chargeStatus == BatteryManager.BATTERY_STATUS_FULL
                    if (isCharging) {
                        if (batteryInfo.batteryLevel >= RecordSyncChecker.BATTERY_CHARGING_MIN_TEMP) {
                            trigSynNow()
                            dismiss()
                        } else {
                            TipStatusManager.syncResult = RESULT_LOW_BATTERY_CHARGING
                            accordingStateShowImage()
                        }
                    } else {
                        if (batteryInfo.batteryLevel >= RecordSyncChecker.BATTERY_UN_CHARGING_MIN_TEMP) {
                            trigSynNow()
                            dismiss()
                        } else {
                            TipStatusManager.syncResult = RESULT_LOW_BATTERY
                            accordingStateShowImage()
                        }
                    }
                }
            }

            RESULT_LOCAL_INSUFFICIENT_SPACE -> {
                //清理内存空间回来，
                // 如果存储大于300可用，自动触发一次云同步
                // 如果存储还是不足300，关闭弹窗，初始化同步状态
                if (RecordSyncChecker.isLocalStorageAvailable(mContext)) {
                    DebugUtil.i(TAG, "isLocalStorageAvailable")
                    trigSynNow()
                } else {
                    TipStatusManager.resetTipsStatus()
                }
                dismiss()
            }
            else -> {
                DebugUtil.i(TAG, "onResume " + getCloudState())
            }
        }
    }

    private fun getCloudState(): Int {
        return TipStatusManager.syncResult
    }

    /**
     * 获取云同步同步方式 上传/下载 ，元数据/文件
     */
    private fun getCloudSynWay(): Int {
        return TipStatusManager.mSyncResultData?.errorFrom
            ?: CloudSyncResult.ERROR_FROM_DEFAULT_CHECKER
    }

    private fun setTextColorAndText(imgRes: Int, textRes: Int, formatStr: String? = null) {
        mImageView?.setImageResource(imgRes)
        if (formatStr == null) {
            mTextView?.setText(textRes)
        } else {
            val format = String.format(mContext.resources.getString(textRes), formatStr)
            mTextView?.text = format
        }
    }

    private fun setButtonText(buttonRes: Int) {
        mButton?.text = mContext.resources.getString(buttonRes)
    }

    /**
     * 根据state
     * 设置弹窗文案
     */
    private fun accordingStateShowImage() {
        DebugUtil.i(TAG, "accordingStateShowImage state = ${getCloudState()}")
        CloudSyncStatusDialogHelper.whenStateChangeReturnRes(mContext, getCloudState(), mCallback)
    }

    /**
     * 重试，在3.5s后需要再次获取状态更新UI
     */
    private fun delayTimeExecute() {
        mHandler.sendEmptyMessageDelayed(MESSAGE_REFRESH_UI, RETRY_DELAY_TIME)
    }

    /**
     * 重置重试
     */
    private fun resetRetry() {
        mButton?.isEnabled = true
        mHandler.removeCallbacksAndMessages(null)
    }

    /**
     * 网络未连接或者开关不匹配需要注册网络监听
     * 网络异常重试，重试过程中需要监听同步状态
     */
    private fun businessOfState() {
        TipStatusManager.addNotifyDialogListener(this)
    }

    /**
     * 弹窗点击事件处理
     * 跳转到其他界面处理的都不需要先dismiss弹窗
     */
    override fun onClick(view: View?) {
        DebugUtil.i(TAG, "onclick state = ${getCloudState()}")
        when (getCloudState()) {
            RESULT_NETWORK_NO_CONNECT, RESULT_NETWORK_TYPE_MISMATCH -> {
                //跳转wifi 开启
                CloudSyncStatusDialogHelper.startToWIFISetting(mContext)
            }
            RESULT_POWER_SAVING_MODE -> {
                //跳转省电
                CloudSyncStatusDialogHelper.startToSavePower(mContext)
            }
            RESULT_LOW_BATTERY,
            RESULT_LOW_BATTERY_CHARGING,
            RESULT_DATA_COLD_STANDBY,
            RESULT_REQUEST_TOO_FREQUENT
            -> {
                //低电量
                // 低电量充电中
                // 数据已归档点击直接关闭
                // 云服务限流
                //关闭弹窗后自动恢复副标题为默认X项录音
                TipStatusManager.resetTipsStatus()
                dismiss()
            }
            RESULT_LOCAL_INSUFFICIENT_SPACE -> {
                //本地空间不足跳转数据清理
                CloudSyncStatusDialogHelper.startToClearData(mContext)
            }

            RESULT_TEMPERATURE_HIGH -> {
                //手机温度过高,需要降温到38及以下可自动恢复   也可继续
                CloudSyncAgent.getInstance().ignoreCheckHighTemperature = true
                trigSynNow()
                dismiss()
            }
            else -> {
                //网络异常重试
                trigSynNow()
                mButton?.isEnabled = false
                setButtonText(R.string.reconnecting)
                delayTimeExecute()
            }
        }
    }

    /**
     * 上传/下载 同步统一方法
     */
    private fun trigSynNow() {
        DebugUtil.i(TAG, "trigSynNow")
        when (getCloudSynWay()) {
            CloudSyncResult.ERROR_FROM_FILE_BACKUP,
            CloudSyncResult.ERROR_FROM_METADATA_BACKUP,
            -> {
                SyncTriggerManager.getInstance(BaseApplication.getAppContext()).trigBackupNow()
            }
            CloudSyncResult.ERROR_FROM_FILE_RECOVERY,
            CloudSyncResult.ERROR_FROM_METADATA_RECOVERY,
            -> {
                SyncTriggerManager.getInstance(BaseApplication.getAppContext()).trigRecoveryNow()
            }
        }
    }

    override fun isShow() = (mDialog != null && mDialog?.isShowing == true)

    override fun show() {
        if (!isShow()) {
            businessOfState()
            accordingStateShowImage()
            mDialog?.show()
        }
    }

    override fun dismiss(runAnim: Boolean) {
        if (isShow()) {
            mDialog?.dismiss(runAnim)
        }
    }

    override fun updateLayoutOnConfig(configuration: Configuration) {
        if (isShow()) {
            mDialog?.updateLayoutWhileConfigChange(configuration)
        }
    }

    override fun release() {
        dismiss(false)
        owner = null
        mFragmentManager = null
        mHandler.removeCallbacksAndMessages(null)
        DebugUtil.e(TAG, "release   mState == ${getCloudState()}")
    }

    override fun doDismiss() {
        dismiss()
    }

    override fun notifyContent() {
        accordingStateShowImage()
    }
}