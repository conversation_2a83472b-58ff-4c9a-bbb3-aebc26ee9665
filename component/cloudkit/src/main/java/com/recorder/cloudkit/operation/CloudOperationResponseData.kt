/*************************************************************************************************
 * Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - CloudOperationResponseData.kt
 * Description: 云端配置下发的数据结构
 *
 * Version: 1.0
 * Date: 2024/9/18
 * Author: 80266877
 * TAG:
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * 80266877  2024/9/18    1.0              build this module
 ************************************************************************************************/
package com.recorder.cloudkit.operation

import android.net.Uri
import com.google.gson.annotations.SerializedName

/**
 * 云端配置下发的数据结构
 */
data class CloudOperationResponseData(
    @SerializedName("resourceInfos") val dataList: List<ConfigData>?
) {

    fun isSame(a: CloudOperationResponseData?): Boolean {
        return a?.dataList?.let {
            if (it.isEmpty()) {
                return false
            }
            it.first().let { configData ->
                configData.remindConfigId.contentEquals(dataList?.first()?.remindConfigId)
            }
        } ?: false
    }
    /**
     * 云端配置下发的全部数据
     */
    data class ConfigData(
        /**
         * 每个提醒配置的唯一ID
         */
        @SerializedName("remindConfigId") val remindConfigId: String,
        /**
         * 配置文案
         */
        @SerializedName("content") val content: String,
        /**
         * 配置类型
         */
        @SerializedName("remindCategory") val remindCategory: RemindCategory,
        /**
         * 配置生效日期，ms
         */
        @SerializedName("startTime") val startTime: Long,
        /**
         * 配置过期日期，ms
         */
        @SerializedName("endTime") val endTime: Long,
        /**
         * 按钮列表，配置1到2个
         */
        @SerializedName("buttons") var buttons: List<ButtonInfo>?,
        /**
         * 资源投放追踪标识
         */
        @SerializedName("trackId") val trackId: String
    ) {

        /**
         * 按钮的全部数据
         */
        data class ButtonInfo(
            /**
             * 位置，0-左边，1-右边
             */
            @SerializedName("index") val index: Int,
            /**
             * 按鈕文案
             */
            @SerializedName("content") val content: String,
            /**
             * 按钮点击操作
             */
            @SerializedName("linkInfo") val linkInfo: LinkInfo
        ) {
            fun getButtonAction(): ButtonAction? {
                val uri = Uri.parse(linkInfo.jumpLink)
                return uri.takeIf {
                    it.toString().startsWith(SCHEMA_OF_NOTE)
                }?.let {
                    when (it.getQueryParameter(TOKEN)) {
                        TOKEN_IGNORE -> ButtonAction.IGNORE
                        TOKEN_CLOUD_SETTING -> ButtonAction.PAGE_CLOUD_SETTING
                        TOKEN_PAGE_HALF_SCREEN_PAYMENT -> ButtonAction.PAY_UPGRADE
                        TOKEN_OPEN_CLOUD_SYNC -> ButtonAction.OPEN_CLOUD_SYNC
                        else -> null
                    }
                }
            }
        }

        /**
         * 按钮点击行为
         */
        data class LinkInfo(
            /**
             * 链接类型
             */
            @SerializedName("linkType") val linkType: LinkType,
            /**
             * 链接内容
             */
            @SerializedName("jumpLink") val jumpLink: String
        )

        /**
         * 配置类型
         */
        enum class RemindCategory {
            /**
             * 开关类型
             */
            SWITCH,

            /**
             * 付费类型
             */
            PAY
        }

        /**
         * 链接类型
         */
        enum class LinkType {
            /**
             * 账号web view，对应后台的web，h5页面
             */
            WEB_VIEW,

            /**
             * 浏览器，跳转到外部浏览器
             */
            BROWSER,

            /**
             * native页面，客户端原生页面，deeplink链接
             */
            NATIVE,

            /**
             * 快应用
             */
            FAST_APP,

            /**
             * 下载地址
             */
            DOWNLOAD
        }
    }

    enum class ButtonAction {
        IGNORE,
        PAGE_CLOUD_SETTING,
        OPEN_CLOUD_SYNC,
        PAY_UPGRADE,
    }
    companion object {
        const val SCHEMA_OF_NOTE = "oplus://note/router"
        const val TOKEN = "token"

        /**
         * 忽略
         */
        const val TOKEN_IGNORE = "ignore"

        /**
         *打开云同步设置按页面
         */
        const val TOKEN_CLOUD_SETTING = "page_cloud_setting"

        /**
         * 同步开关打开
         */
        const val TOKEN_OPEN_CLOUD_SYNC = "sw_auto_sync"

        /**
         * 操作：打开支付半屏 前提：无
         */
        const val TOKEN_PAGE_HALF_SCREEN_PAYMENT = "page_half_screen_payment"
    }
}
