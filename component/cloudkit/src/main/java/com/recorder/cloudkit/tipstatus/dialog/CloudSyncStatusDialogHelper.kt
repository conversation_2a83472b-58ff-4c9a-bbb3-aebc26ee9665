package com.recorder.cloudkit.tipstatus.dialog

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.BatteryManager
import android.provider.Settings
import com.recorder.cloudkit.R
import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode
import com.recorder.cloudkit.sync.config.CloudConfigUtil
import com.recorder.cloudkit.tipstatus.TipStatusManager
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.base.utils.OS12FeatureUtil
import com.soundrecorder.base.utils.OplusCompactUtil
import com.soundrecorder.base.utils.RtlUtils
import com.soundrecorder.common.buryingpoint.CloudStaticsUtil
import com.soundrecorder.modulerouter.cloudkit.CloudSwitchState


object CloudSyncStatusDialogHelper {
    const val TAG = "CloudSyncStatusDialogHelper"

    /**
     * 跳转云服务录音空间的uri
     * 快应用url+enter_id（9：app#01recording）
     * */
    private const val URI_CLOUD_QUICK_APP = "hap://app/com.heytap.cloudservice/page/audio?enter_id=9"

    /*跳转到省电模式开关页面 start*/
    private const val BATTERY_PACKAGE_NAME = "com.oplus.battery"
    private const val BATTERY_SAVE_MODE_PAGE = "com.oplus.powermanager.fuelgaue.PowerSaveActivity"
    private const val BATTERY_SAVE_MODE_ACTION_OS13 = "oplus.intent.action.PowerSaveActivity"

    /*OS11.3以下使用包名、activity*/
    private const val BATTERY_PACKAGE_NAME_OS11_3 = "com.coloros.oppoguardelf"
    private const val BATTERY_SAVE_MODE_PAGE_OS11_3 = "com.coloros.powermanager.fuelgaue.PowerSaveActivity"
    /*跳转省电模式页面end*/

    /*数据清理页面*/
    private const val PHONE_MANAGER_PACKAGE = "com.coloros.phonemanager"
    /*11.3及以前使用action*/
    private const val CLEAR_DATA_ACTIVITY_ACTION_BEFORE = "com.oppo.cleandroid.ui.ClearMainActivity"
    /*OS11.3以后使用action*/
    private const val CLEAR_DATA_ACTIVITY_ACTION_AFTER = "oplus.intent.action.CLEAR_MAIN_ACTIVITY"


    fun whenStateChangeReturnRes(context: Context, state: Int, callBack: (Int, Int, Int, format: String?) -> Unit) {
        when (state) {
            /**
             * 无网络链接和开关不匹配    都去开启wifi
             * ①未勾选“允许使用移动网络同步”，在数据同步过程中“WLAN关闭或处于不可连接”状态
             * 【半屏文案】 WLAN未连接，同步暂停 连接 WLAN
             * ②已勾选“允许使用移动网络同步”，在数据同步过程中“WLAN关闭或处于不可连接”状态，且“移动数据关闭或处于不可连接”状态
             * 【文案】 网络未连接，同步暂停 连接 WLAN
             *
             * 【3】触发同步，进行元数据恢复时无网络 或 已勾选“允许使用移动网络同步”，触发同步，进行元数据恢复时无网络
             * 【半屏文案】未查询到云端数据  连接 WLAN
             */
            SyncErrorCode.RESULT_NETWORK_NO_CONNECT, SyncErrorCode.RESULT_NETWORK_TYPE_MISMATCH -> {
                if (TipStatusManager.mSyncResultData?.inQueryStep == true) {
                    callBack.invoke(
                        R.drawable.ic_noconnnection,
                        R.string.query_data_failed,
                        R.string.sync_connect_wlan,
                        null
                    )
                } else {
                    callBack.invoke(
                        R.drawable.ic_noconnnection,
                        if (TipStatusManager.syncSwitch == CloudSwitchState.OPEN_ALL) R.string.network_error_sync_pause else R.string.network_error_not_sync,
                        R.string.sync_connect_wlan,
                        null
                    )
                }
            }
            SyncErrorCode.RESULT_POWER_SAVING_MODE -> {
                callBack.invoke(
                    R.drawable.ic_savepower,
                    R.string.open_save_power_mode_sync_pause,
                    R.string.turn_off,
                    null
                )
            }
            //低电量直接显示电量小于20
            SyncErrorCode.RESULT_LOW_BATTERY -> {
                var imgResId = R.drawable.ic_lower_power_20
                var formatStr = "20%"
                if (BaseApplication.sIsRTLanguage) {
                    formatStr = RtlUtils.addDirectionSymbolForRtl(formatStr).toString()
                }
                callBack.invoke(
                    imgResId,
                    R.string.lower_power,
                    R.string.know,
                    formatStr
                )
            }
            SyncErrorCode.RESULT_LOW_BATTERY_CHARGING -> {
                //电量低于10且充电中
                var formatStr = "10%"
                if (BaseApplication.sIsRTLanguage) {
                    formatStr = RtlUtils.addDirectionSymbolForRtl(formatStr).toString()
                }
                callBack.invoke(
                    R.drawable.ic_lowe_power_10,
                    R.string.lower_power_unchar,
                    R.string.know,
                    formatStr
                )
            }
            /**
             * 本地空间不足
             * 【场景】从云端下载数据到本地过程中，因本地空间不足导致数据同步暂停
             * 【半屏文案】存储空间不足，同步暂停  清理存储
             *
             * 【场景】同步开始前，查询元数据时判断本地空间小于300MB时，为保证程序的正常运行，不会拉起同步任务。
             * 【半屏文案】 存储空间不足，同步未开始 清理存储
             */
            SyncErrorCode.RESULT_LOCAL_INSUFFICIENT_SPACE -> {
                callBack.invoke(
                    R.drawable.ic_cloud_full_stroge,
                    if (TipStatusManager.mSyncResultData?.inQueryStep == true) R.string.local_storage_full_sync_not_start else R.string.local_storage_full_sync_pause,
                    R.string.clear_storage,
                    null
                )
            }

            SyncErrorCode.RESULT_DATA_COLD_STANDBY -> {
                callBack.invoke(
                    R.drawable.ic_data_document,
                    R.string.data_archived,
                    R.string.know,
                    null
                )
            }

            SyncErrorCode.RESULT_TEMPERATURE_HIGH -> {
                callBack.invoke(
                    R.drawable.ic_heigh_temp_state,
                    R.string.device_temperature_high_tips,
                    R.string.go_on,
                    null
                )
            }

            SyncErrorCode.RESULT_NETWORK_ERROR -> {
                if (TipStatusManager.mSyncResultData?.inQueryStep == true) {
                    callBack.invoke(
                        R.drawable.ic_noconnection_sync,
                        R.string.network_anomaly,
                        R.string.reconnect,
                        null
                    )
                } else {
                    callBack.invoke(
                        R.drawable.ic_noconnection_sync,
                        R.string.network_anomaly_sync_pause,
                        R.string.reconnect,
                        null
                    )
                }
            }
            /**
             * 【场景】数据同步时，返回云服务限流错误码的场景
             * 【半屏文案】当前处于数据同步高峰期，请稍后尝试
             */
            SyncErrorCode.RESULT_REQUEST_TOO_FREQUENT -> {
                callBack.invoke(
                    R.drawable.ic_height_user_sync,
                    R.string.server_sync_peak_period,
                    R.string.know,
                    null
                )
            }
            else -> {
                if (TipStatusManager.mSyncResultData?.inQueryStep == true) {
                    callBack.invoke(
                        R.drawable.ic_server_error_state,
                        R.string.server_error_sync_not_start,
                        R.string.reconnect,
                        null
                    )
                } else {
                    callBack.invoke(
                        R.drawable.ic_server_error_state,
                        R.string.server_error_sync_pause,
                        R.string.reconnect,
                        null
                    )
                }
            }
        }
    }

    //获取电量和是否在充电
    fun getBatteryLevelAndChargingStatus(context: Context): Pair<Int, Boolean> {
        val batteryManager = context.getSystemService(Context.BATTERY_SERVICE) as BatteryManager
        val level = batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY)
        val charging = batteryManager.isCharging
        val pair: Pair<Int, Boolean> = Pair(level, charging)
        DebugUtil.i(TAG, " first = ${pair.first}  second = ${pair.second}")
        return pair
    }

    /**
     * 同步完成，点击查看-跳转云服务快应用页面
     * 外销：跳转 H5
     * 内销：跳转快应用
     */
    fun startToCloudApp(context: Context): Boolean {
        val success = if (FeatureOption.OPLUS_VERSION_EXP) {
            toCloudH5Link(context)
        } else {
            toCloudQuickApp(context)
        }
        DebugUtil.i(TAG, "startToCloudApp  $success")
        CloudStaticsUtil.addCloudTipsClickViewDataEvent()
        return success
    }

    /**
     * 跳转快应用
     */
    private fun toCloudQuickApp(context: Context): Boolean {
        CloudConfigUtil.getCloudQuickAppLink(context)?.let {
            try {
                val uri = Uri.parse(it)
                val intent = Intent(Intent.ACTION_VIEW, uri)
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                context.startActivity(intent)
                return true
            } catch (e: Exception) {
                DebugUtil.e(TAG, "toCloudQuickApp error $e")
            }
        }
        return false
    }

    /**
     * 跳转云服务H5地址
     */
    private fun toCloudH5Link(context: Context): Boolean {
        CloudConfigUtil.getCloudAppH5Link(context)?.let {
            try {
                // 内销需要增加协议，外销不需要
                val targetUrl = if (FeatureOption.OPLUS_VERSION_EXP) it else "heytapbrowser://webpage/view?url=$it"
                val uri = Uri.parse(targetUrl)
                val intent = Intent(Intent.ACTION_VIEW, uri)
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(intent)
                return true
            } catch (e: Exception) {
                DebugUtil.e(TAG, "toCloudH5Link error $e")
            }
        }
        return false
    }

    //跳转至省电模式页面
    fun startToSavePower(context: Context): Boolean {
        try {
            val intent = Intent()
            val pkgName = if (OplusCompactUtil.isOver11dot3()) BATTERY_PACKAGE_NAME else BATTERY_PACKAGE_NAME_OS11_3
            intent.`package` = pkgName
            if (OS12FeatureUtil.isColorOs13OrLater()) {
                //action 是OS13才加的（为了13的新特性）
                intent.action = BATTERY_SAVE_MODE_ACTION_OS13
            } else {
                val className = if (OplusCompactUtil.isOver11dot3()) BATTERY_SAVE_MODE_PAGE else BATTERY_SAVE_MODE_PAGE_OS11_3
                val componentName = ComponentName(pkgName, className)
                intent.component = componentName
            }
            context.startActivity(intent)
            return true
        } catch (e: Throwable) {
            DebugUtil.e(TAG, "startToSavePower", e)
        }
        return false
    }

    //跳转至wifi页面
    fun startToWIFISetting(context: Context): Boolean {
        try {
            val intent = Intent(Settings.ACTION_WIFI_SETTINGS)
            context.startActivity(intent)
            return true
        } catch (e: Exception) {
            DebugUtil.e(TAG, "startToWIFISetting", e)
        }
        return false
    }

    //跳转至数据清理页面
    fun startToClearData(context: Context): Boolean {
        try {
            val intent = Intent()
            intent.`package` = PHONE_MANAGER_PACKAGE
            OplusCompactUtil.getActionForIntent(intent, CLEAR_DATA_ACTIVITY_ACTION_BEFORE, CLEAR_DATA_ACTIVITY_ACTION_AFTER)
            context.startActivity(intent)
            return true
        } catch (e: Throwable) {
            DebugUtil.e(TAG, "startToClearData", e)
        }
        return false
    }
}