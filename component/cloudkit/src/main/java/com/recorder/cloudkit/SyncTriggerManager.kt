/***********************************************************
 * Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 * VENDOR_EDIT
 * File: SyncTriggerManager
 * Description:
 * Version: 1.0
 * Date : 2019-07-11
 * Author: huang<PERSON>wang
 *
 * v1.0, 2019-3-12, huang<PERSON><PERSON>, create
 */
package com.recorder.cloudkit

import android.content.Context
import android.os.Build
import android.os.Handler
import android.os.HandlerThread
import android.os.Message
import com.heytap.cloudkit.libsync.cloudswitch.bean.CloudSyncState
import com.heytap.cloudkit.libcommon.bean.io.CloudStopType
import com.heytap.cloudkit.libsync.ext.CloudSyncManager
import com.heytap.cloudkit.libsync.metadata.helper.CloudBackupRequestSource
import com.heytap.cloudkit.libsync.metadata.helper.CloudRecoveryRequestSource
import com.heytap.cloudkit.libsync.service.CloudDataType
import com.recorder.cloudkit.global.CloudGlobalStateManager
import com.recorder.cloudkit.push.CloudPushAgent
import com.recorder.cloudkit.sync.CloudSynStateHelper
import com.recorder.cloudkit.sync.RecordSyncChecker
import com.recorder.cloudkit.sync.RecordSyncController
import com.recorder.cloudkit.sync.SyncDataConstants
import com.recorder.cloudkit.tipstatus.TipStatusManager
import com.recorder.cloudkit.utils.CloudPermissionUtils
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.buryingpoint.CloudStaticsUtil
import com.soundrecorder.common.db.CloudSyncRecorderDbUtil
import com.soundrecorder.common.utils.OifaceBindUtils

class SyncTriggerManager private constructor(val mContext: Context) {

    companion object {
        private const val TAG = "SyncTriggerManager"

        // 仅备份
        const val BACKUP = 2

        // 恢复流程 + 备份
        const val RECOVERY = 1

        /*------------------------------------------*/
        private const val MSG_INTERVAL_TIME = 500
        const val RECOVERY_FROM_START_APP = 0
        const val RECOVERY_FROM_MANUAL = 1
        const val RECOVERY_FROM_PUSH = 2
        const val RECOVERY_FROM_FETCH = 3
        const val RECOVERY_FROM_FULL_PUSH = 4

        @Volatile
        private var sInstance: SyncTriggerManager? = null

        @Synchronized
        @JvmStatic
        fun getInstance(context: Context): SyncTriggerManager =
            sInstance ?: synchronized(SyncTriggerManager::class.java) {
                sInstance ?: SyncTriggerManager(context.applicationContext).apply { sInstance = this }
            }

        @Synchronized
        @JvmStatic
        fun release() {
            sInstance?.apply {
                mHandler?.removeCallbacksAndMessages(null)
                stopSyncSync {
                    mSyncController.release()
                    try {
                        /** 放到最后，可能会异常,需要try-catch
                         * stopService会导致之前的callBack收不到，stop时机需要更改到所有callBack回来后，暂时不处理*/
                        if (CloudSyncManager.getInstance().isServiceAvailable) {
                            // unbindService 不会执行onDisConnect，导致service未置空，unbind异常
                            CloudSyncManager.getInstance().stopCloudSyncService()
                            mInitSuccess = false
                        }
                    } catch (_: Exception) {
                        mInitSuccess = false
                        DebugUtil.e(TAG, "stopService error")
                    }

                    releaseHandlerThread()
                }
            }
            sInstance = null
        }
    }

    private var mHandlerThread: HandlerThread? = null
    private var mHandler: Handler? = null
    private var hasBindLittleCore = false
    var mSyncController: RecordSyncController = RecordSyncController(mContext)

    @Volatile
    private var mInitSuccess = false

    init {
        initHandlerThread()
    }

    /**
     * 触发同步，此处针对数据改动
     */
    fun trigBackupNow() {
        if (!canInterceptCloudSync()) {
            trigBackupNow(false)
        }
    }

    /**
     * 触发同步，此处针对数据改动
     */
    private fun trigBackupNow(scanMedia: Boolean = false) {
        if (canInterceptCloudSync()) {
            DebugUtil.d(TAG, "trigBackupNow, region is not support or no netWork")
            return
        }
        mHandler?.removeMessages(BACKUP)
        mHandler?.obtainMessage(BACKUP)?.let {
            it.obj = scanMedia
            mHandler?.sendMessageDelayed(it, MSG_INTERVAL_TIME.toLong())
        }
    }

    /**
     * 触发恢复
     * @param from 对应CloudRecoveryRequestSource的4种类型
     */
    fun trigRecoveryNow(from: Int = RECOVERY_FROM_START_APP) {
        if (canInterceptCloudSync()) {
            DebugUtil.d(TAG, "trigRecoveryNow, region is not support or no netWork")
            return
        }
        trigRecoveryNow(false, from)
    }

    /**
     * 触发恢复
     * @param scanMedia 是否要对比媒体库音频文件 true：先执行全量对比后再触发同步流程，false：直接触发同步流程
     * @param from 对应CloudRecoveryRequestSource的4种类型
     */
    fun trigRecoveryNow(scanMedia: Boolean = false, from: Int = RECOVERY_FROM_START_APP) {
        if (canInterceptCloudSync()) {
            return
        }
        CloudStaticsUtil.addCloudLog(TAG, "trigRecoveryNow,scanMedia=$scanMedia,from=$from")
        mHandler?.removeMessages(RECOVERY)
        mHandler?.obtainMessage(RECOVERY)?.let {
            it.obj = scanMedia
            it.arg1 = from
            mHandler?.sendMessageDelayed(it, MSG_INTERVAL_TIME.toLong())
        }
    }

    /**
     * 停止同步，删除同步记录及锚点等信息
     * @param deleteData 是否删除本地已同步过的文件
     * 停止同步流程 + 清空 cloudkit 锚点
     * true： 删除recorder.db中已同步过的记录 以及文件
     * false： 更新recorder.db中已同步过的记录的同步状态信息
     */
    fun trigStopSyncForLoginOut(deleteData: Boolean = false) {
        if (!CloudSynStateHelper.isRegionCloudSupport()) {
            return
        }
        CloudStaticsUtil.addCloudLog(TAG, "trigStopSyncForLoginOut,deleteData=$deleteData")
        mHandler?.apply {
            removeMessages(RECOVERY)
            removeMessages(BACKUP)
        }
        stopSyncSync {
            //sdk里面有关和账号绑定的东西，包括锚点、开关需要业务方单独处理关闭
            CloudSyncManager.getInstance().clearUserDataOnLogout()
            // 清除分页锚点
            CloudSyncManager.getInstance().clearSysVersion(SyncDataConstants.MODULE_RECORDER, SyncDataConstants.ZONE_RECORDER)
            CloudSyncManager.getInstance().clearSysVersionByDataType(CloudDataType.PUBLIC) // 清空业务数据
            CloudSyncRecorderDbUtil.handleSyncDataForLoginout(mContext, deleteData)
        }
    }

    fun trigStopSyncForClearAnchor() {
        if (!CloudSynStateHelper.isRegionCloudSupport()) {
            return
        }
        DebugUtil.i(TAG, "trigStopSyncForClearAnchor")
        CloudStaticsUtil.addCloudLog(TAG, "trigStopSyncForClearAnchor")
        mHandler?.apply {
            removeMessages(RECOVERY)
            removeMessages(BACKUP)
        }
        stopSyncSync {
            // 清除锚点
            CloudSyncManager.getInstance().clearSysVersion(SyncDataConstants.MODULE_RECORDER, SyncDataConstants.ZONE_RECORDER)
            CloudSyncManager.getInstance().clearSysVersionByDataType(CloudDataType.PUBLIC)
        }
    }

    fun trigStopSyncForErrorCode(errorCode: Int?) {
        if (!CloudSynStateHelper.isRegionCloudSupport()) {
            return
        }
        DebugUtil.i(TAG, "trigStopSyncForNothing,errorCode=$errorCode")
        CloudStaticsUtil.addCloudLog(TAG, "trigStopSyncForNothing,errorCode=$errorCode")
        mHandler?.apply {
            removeMessages(RECOVERY)
            removeMessages(BACKUP)
        }
        stopSyncSync(bizSubErrorCode = errorCode)
    }

    /**
     * @param stopType 停止类型：CloudStopType.LIMIT（快稳省停止）或CloudStopType.MANUAL（手动停止）
     * @param bizSubErrorCode 业务方透传错误码，业务方自定义数据，最终会透传到startBackupMetaData()的onError()回调方法中的CloudKitError参数的getBizSubErrorCode()
     */
    private fun stopSyncSync(stopType: Int = CloudStopType.MANUAL, bizSubErrorCode: Int? = null, moreFunction: (() -> Unit)? = null) {
        if (mSyncController.isSyncRunning()) {
            mSyncController.setManualStop(true, bizSubErrorCode) // 业务标识停止同步
        }
        mHandler?.post {
            try {
                if (mSyncController.isSyncRunning()) {
                    CloudSyncManager.getInstance()
                        .stopTransferFilesByModule(SyncDataConstants.MODULE_RECORDER, stopType, bizSubErrorCode ?: 0)  // 停止文件传输
                    CloudSyncManager.getInstance().stopAllBackupAndRecoveryMetaData(stopType, bizSubErrorCode ?: 0) //停止元数据
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                        CloudSyncManager.getInstance().setSyncState(CloudSyncState.DEFAULT) //清除同步状态
                    }
                }
                CloudSyncManager.getInstance().deleteAllTransferFiles() // 删除本地的文件上传下载记录
                mSyncController.reset()
            } catch (_: Exception) {
                mSyncController.reset()
                DebugUtil.e(TAG, "stopSyncSync error")
            }
            moreFunction?.invoke()
        }
    }

    fun scheduleWorkerJob(runnable: Runnable?, delayTime: Long): Boolean {
        if (!CloudSynStateHelper.isRegionCloudSupport()) {
            return false
        }
        if (runnable != null) {
            mHandler?.removeCallbacks(runnable)
            mHandler?.postDelayed(runnable, delayTime)
            return true
        }
        return false
    }

    /**
     * 不判断是否支持云同步，日志打捞使用，不支持云同步的代码也需要日志打捞，
     */
    fun scheduleWorkerJobWithoutCheckCloud(runnable: Runnable?, delayTime: Long) {
        if (runnable != null) {
            mHandler?.removeCallbacks(runnable)
            mHandler?.postDelayed(runnable, delayTime)
        }
    }

    private fun initHandlerThread() {
        //这个地方注释是由于需要日志打捞需要当前mHandlerThread初始化，不能在这个地方加入是否支持云同步的判断
        mHandlerThread = HandlerThread("handler_thread").apply {
            start()
            //线程绑定小核
            val threadId = looper.thread.id
            if (!hasBindLittleCore) {
                OifaceBindUtils.getInstance().bindTaskWithOiface(
                    OifaceBindUtils.BIND_TASK, threadId.toInt())
                hasBindLittleCore = true
                DebugUtil.i(TAG, "handler thread bind littlcore", true)
            }
        }
        val looper = mHandlerThread?.looper ?: return
        mHandler = object : Handler(looper) {
            override fun handleMessage(message: Message) {
                when (message.what) {
                    BACKUP -> {
                        // 避免云同步关闭的时候，执行该方法
                        if (!TipStatusManager.isCloudOn()) {
                            DebugUtil.i(TAG, "switch state is off")
                            return
                        }
                        mSyncController.doBackUp(CloudBackupRequestSource.DATA_CHANGE)
                    }
                    RECOVERY -> {
                        ensureCloudServiceInitial()
                        val recoveryRequestSource = convertToRecoveryRequestSource(message.arg1)
                        if (recoveryRequestSource == CloudRecoveryRequestSource.MANUAL) {
                            // 打开开关，云端全量，记录全量时间
                            RecordSyncChecker.updateFullRecoveryTime(mContext)
                        }
                        mSyncController.doRecovery(message.obj as Boolean, recoveryRequestSource)
                    }
                }
                super.handleMessage(message)
            }
        }
    }

    private fun releaseHandlerThread() {
        mHandlerThread?.apply { //线程解绑小核
            val threadId = looper.thread.id
            if (hasBindLittleCore) {
                OifaceBindUtils.getInstance().bindTaskWithOiface(
                    OifaceBindUtils.UNBIND_TASK, threadId.toInt())
                hasBindLittleCore = false
                DebugUtil.i(TAG, "handler thread unbind littlcore", true)
            }
            quit()
        }
        mHandler = null
        mHandlerThread = null
    }

    private fun ensureCloudServiceInitial() {
        if (!mInitSuccess && CloudPermissionUtils.isNetWorkNoticeGranted(mContext)) {
            // 主动调用 startCloudSyncService，setEnableCtaRequest就置为true了
            CloudSyncManager.getInstance().setEnableCtaRequest(true)
            val pushId = CloudPushAgent.getPushRegisId()
            if (pushId != null) {
                CloudSyncManager.getInstance().registerPush(CloudPushAgent.getPushRegisId())
            }
            mInitSuccess = CloudSyncManager.getInstance().startCloudSyncService()
            DebugUtil.i(TAG, "startCloudSyncService $mInitSuccess pushId: ${CloudPushAgent.getPushRegisId()}", true)
        }
    }

    private fun convertToRecoveryRequestSource(from: Int): CloudRecoveryRequestSource {
        return when (from) {
            RECOVERY_FROM_START_APP -> CloudRecoveryRequestSource.START_APP
            RECOVERY_FROM_MANUAL -> CloudRecoveryRequestSource.MANUAL
            RECOVERY_FROM_PUSH -> CloudRecoveryRequestSource.PUSH
            RECOVERY_FROM_FETCH -> CloudRecoveryRequestSource.NEED_FETCH
            RECOVERY_FROM_FULL_PUSH -> CloudRecoveryRequestSource.PUSH_FULL_RECOVERY
            else -> CloudRecoveryRequestSource.OTHERS
        }
    }

    fun canInterceptCloudSync(): Boolean {
        return !CloudSynStateHelper.isRegionCloudSupport()
                || !CloudGlobalStateManager.canDoSyncProcess()
                || !CloudPermissionUtils.isNetWorkNoticeGranted(BaseApplication.getAppContext())
    }
}