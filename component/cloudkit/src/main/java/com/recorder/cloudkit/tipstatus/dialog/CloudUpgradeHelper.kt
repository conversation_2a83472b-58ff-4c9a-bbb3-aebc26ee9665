package com.recorder.cloudkit.tipstatus.dialog

import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.heytap.cloudkit.libpay.upgrade.CloudUpgradeAgent
import com.heytap.cloudkit.libpay.upgrade.CloudUpgradeDialogBuilder
import com.heytap.cloudkit.libpay.upgrade.http.request.CloudGetUpgradeActivityRequest
import com.heytap.cloudkit.libpay.upgrade.http.response.CloudGetUpgradeResponse
import com.heytap.cloudkit.libsync.cloudswitch.bean.SwitchState
import com.soundrecorder.base.utils.DebugUtil
import com.recorder.cloudkit.SyncTriggerManager
import com.recorder.cloudkit.sync.CloudSynStateHelper
import com.recorder.cloudkit.sync.SyncDataConstants
import com.soundrecorder.modulerouter.cloudkit.dialog.ICloudUpgradeHelper
import kotlinx.coroutines.*

/**
 * 云空间升级
 */
class CloudUpgradeHelper : ICloudUpgradeHelper {
    private val TAG = "CloudUpgradeHelper"
    private var mUpgradeDialog: DialogFragment? = null

    /**
     * 获取套餐详情，显示升级弹窗
     */
    override fun upgradeCloudSpace(fragment: Fragment?) {
        if (mUpgradeDialog?.isResumed == true) {
            return
        }
        fragment?.lifecycleScope?.launch(Dispatchers.IO) {
            // 1、业务方需要先判断账号登陆状态
            if (!CloudSynStateHelper.isLoginFromCache()) {
                DebugUtil.e(TAG, "upgradeCloudSpace: not login")
                return@launch
            }
            val upgradeCloudResponse = getUpgradeCloudDetail()
            // 如果upgradeCloudResponse出错，直接传null即可（build(null)），不影响主流程!
            withContext(Dispatchers.Main) {
                showUpgradeCloudDialog(upgradeCloudResponse, fragment)
            }
        }
    }

    /**
     * 释放资源
     */
    override fun releaseDialog() {
        mUpgradeDialog?.dialog?.dismiss()
        mUpgradeDialog = null
    }

    /**
     * 获取云空间套餐详情
     */
    private fun getUpgradeCloudDetail(): CloudGetUpgradeResponse? {
        val request = CloudGetUpgradeActivityRequest(0, 0, false)
        request.module = SyncDataConstants.MODULE_RECORDER
        val switchState = CloudSynStateHelper.getSwitchState()
        val switchStatus = switchState > SwitchState.CLOSE.state
        request.isSwitchStatus = switchStatus
        return CloudUpgradeAgent().getUpgradeInfo(request)
    }

    /**
     * 升级云空间需要传入fragmentManager 展示套餐详情
     */
    private fun showUpgradeCloudDialog(
        response: CloudGetUpgradeResponse?,
        fragment: Fragment?,
    ) {
        if (mUpgradeDialog?.isResumed == true) {
            return
        }
        runCatching {
            fragment?.lifecycleScope?.launchWhenResumed {
                DebugUtil.i(TAG, "showUpgradeCloudDialog response $response")
                mUpgradeDialog = CloudUpgradeDialogBuilder(SyncDataConstants.MODULE_RECORDER)
                    .setCallback {
                        if (it) {
                            val context = fragment.context?.applicationContext ?: return@setCallback
                            SyncTriggerManager.getInstance(context).trigRecoveryNow()
                        }
                    }
                    .build(response)
                mUpgradeDialog?.show(fragment.parentFragmentManager, TAG)
            }
        }.onFailure {
            DebugUtil.e(TAG, "showUpgradeCloudDialog error ${it.message}")
        }
    }
}