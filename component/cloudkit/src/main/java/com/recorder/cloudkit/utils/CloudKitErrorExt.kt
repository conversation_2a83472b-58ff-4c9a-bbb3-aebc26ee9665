package com.recorder.cloudkit.utils

import android.content.Context
import com.heytap.cloudkit.libcommon.netrequest.error.CloudBizError
import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.soundrecorder.base.BaseApplication
import com.recorder.cloudkit.sync.RecordSyncChecker
import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode

object CloudKitErrorExt {

    fun CloudKitError.canRetry(context: Context): Boolean {
        if (bizErrorCode == CloudBizError.NETWORK.code) {
            return RecordSyncChecker.hasInternetConnect(context)
        }
        if (bizErrorCode == CloudBizError.FAIL.code) {
            return (subServerErrorCode in SyncErrorCode.CAN_TRY_SERVER_ERROR_CODE)
        }
        return false
    }

    /**
     * 同步流程中，返回错误码是否影响后面的同步流程
     * @return true: 不影响，可接着处理下一步
     */
    fun CloudKitError?.canContinueSyncProcess(context: Context): Boolean {
        if (this == null) {
            return true
        }
        return when (bizErrorCode) {
            CloudBizError.SUCCESS.code -> true
            CloudBizError.FAIL.code -> subServerErrorCode in SyncErrorCode.CAN_TRY_SERVER_ERROR_CODE
            CloudBizError.NETWORK.code -> RecordSyncChecker.hasInternetConnect(context)
            //上传文件，文件找不到
            CloudBizError.NO_FIND_LOCAL_FILE.code -> true

            else -> return false
        }
    }

    fun CloudKitError?.calFinalErrorCode(): Int {
        if (this == null) {
            return SyncErrorCode.RESULT_SUCCESS
        }
        return when (bizErrorCode) {
            CloudBizError.FAIL.code -> subServerErrorCode
            CloudBizError.LIMIT_STOP.code -> bizSubErrorCode
            CloudBizError.MANUAL_STOP.code -> bizSubErrorCode
            // 业务再此统一处理弱网、无网络 区分开来，UI那块直接使用不用再次判断
            CloudBizError.NETWORK.code -> {
                if (RecordSyncChecker.hasInternetConnect(BaseApplication.getAppContext())) SyncErrorCode.RESULT_NETWORK_ERROR else SyncErrorCode.RESULT_NETWORK_NO_CONNECT
            }
            else -> {
                bizErrorCode
            }
        }
    }

    fun CloudKitError?.logMessage(): String {
        if (this == null) {
            return "[cloudKitError=null]"
        }
        return "[cloudKitError={bizErrorCode=$bizErrorCode},subServerErrorCode=$subServerErrorCode,innerErrorCode=$innerErrorCode,errorMsg=$errorMsg]"
    }
}