package com.recorder.cloudkit.sync.bean

import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode

class SyncCheckResult(val resultCode: Int = SyncErrorCode.RESULT_SUCCESS) {

    fun success() = resultCode == SyncErrorCode.RESULT_SUCCESS

    fun message(): String = when (resultCode) {
        SyncErrorCode.RESULT_LOCAL_INSUFFICIENT_SPACE -> "error message: local storage is nor available"
        SyncErrorCode.RESULT_NETWORK_NO_CONNECT -> "error message:no internet"
        SyncErrorCode.RESULT_NETWORK_TYPE_MISMATCH -> "error message: internet not match with cloud sync"
        SyncErrorCode.RESULT_PERMISSION_DENIED -> "error message: no permission"
        SyncErrorCode.RESULT_SWITCH_CLOSE -> "error message: cloud sync switch is closed"
        SyncErrorCode.RESULT_POWER_SAVING_MODE -> "error message: phone open power saving mode"
        SyncErrorCode.RESULT_LOW_BATTERY -> "error message: low battery"
        SyncErrorCode.RESULT_LOW_BATTERY_CHARGING -> "error message: low battery in charging"
        SyncErrorCode.RESULT_TEMPERATURE_HIGH -> "error message: battery temperature is high"
        else -> "unknown code message,code is $resultCode"
    }
}
