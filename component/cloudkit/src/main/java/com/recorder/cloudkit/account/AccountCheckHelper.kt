package com.recorder.cloudkit.account

import android.content.Context
import android.content.pm.PackageManager
import android.content.pm.PackageManager.NameNotFoundException
import android.os.Build
import android.text.TextUtils
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.recorder.cloudkit.SyncTriggerManager
import com.recorder.cloudkit.sync.CloudSynStateHelper
import com.recorder.cloudkit.tipstatus.TipStatusManager
import com.recorder.cloudkit.utils.CloudPermissionUtils
import com.soundrecorder.modulerouter.cloudkit.CloudSwitchState

/**
 * 1.check 上次打开录音跟本地打开登录的不是同一个用户的逻辑处理
 * 2.处理账号SDK问题：
 * 安装老版本录音(未接入账号SDK)且已登录的状态下，升级为CK版本(接入账号SDK)，通过账号SDK获取用户信息获取不到
 * 问题原因：
 * 账号登录成功后，会给手机上已集成账号SDK应用下发TOKEN，就能通过账号SDK拿到正确的用户信息
 */
class AccountCheckHelper {
    companion object {
        const val ACCOUNT_PKG_OPPO_R = "com.oppo.usercenter"
        const val ACCOUNT_PKG_OPPO_S = "com.oplus.vip"
        const val ACCOUNT_PKG_REAL_ME = "com.heytap.usercenter"

        /*relame Q 以下的是8230*1000,录音不上Q，暂无影响*/
        const val ACCOUNT_MIN_VERSION_CODE = 82300
        var sShowLoginPage = true
        val sAccountCheckHelper: AccountCheckHelper = AccountCheckHelper()

        @JvmStatic
        fun getInstance(): AccountCheckHelper = sAccountCheckHelper
    }

    private val mLogTag = "AccountCheckHelper"

    fun checkAccount(context: Context) {
        val lastLoginId = AccountPref.getAccountUId(context)
        val currentLoginId = AccountManager.sAccountManager.getLoginIdFromCache(context)
        DebugUtil.i(mLogTag, "checkAccount finish oldLoginId:$lastLoginId,newLoginId:$currentLoginId", true)

        /*上一次为已登录， 前后两次登录用户不一致*/
        if (!TextUtils.isEmpty(lastLoginId) && lastLoginId != currentLoginId) {
            // 停止当前同步流程、清空已同步数据记录、锚点
            SyncTriggerManager.getInstance(context).trigStopSyncForLoginOut(false)
            // 设置开关状态为关闭。云服务开关页面是打开的录音页面，可以解决切换账号后去云服务APP打开开关后，更新sp
            CloudSynStateHelper.setSyncSwitch(CloudSwitchState.CLOSE, false)
            AccountPref.setAccountUId(context, currentLoginId)
        } else if ((lastLoginId.isNullOrBlank()) && (currentLoginId.isNullOrBlank()) && (!AccountPref.getUpgradeAccountTag(context))) {
            /*初次升级到CK版本，无账号SDK升级到接入账号SDK，解决无法获取到账号登录问题*/
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                handleRecordUpgrade(context)
            } else {
                DebugUtil.e(mLogTag, "sdk below R")
            }
        } else {
            AccountPref.setAccountUId(context, currentLoginId)
        }
    }

    /**
     * 录音版本升级，老版本未接入账号SDK，新版本接入后，无法获取到账号登录态（受账号当前技术设计限制）
     * 需要调用张宏SDK刷新账号信息
     */
    private fun handleRecordUpgrade(context: Context) {
        DebugUtil.i(mLogTag, " handleRecordUpgrade")
        // 1. 账号APP 是否满足 82300及以上，不满足则直接设置云同步为关闭状态
        if (getAccountAPPVersionCode(context) < ACCOUNT_MIN_VERSION_CODE) {
            /*账号APP版本不满足要求，关闭云同步开关*/
            //非用户主动关闭，不设置云同步开关状态
            CloudSynStateHelper.setSyncSwitch(CloudSwitchState.CLOSE, false)
            // 记录已处理初次升级上来问题
            AccountPref.setUpgradeAccountTag(context)
            return
        }
        // 2. 设置不拉起登录页
        sShowLoginPage = false
        val isNetWorkGranted = CloudPermissionUtils.isNetWorkNoticeGranted(context)
        if (!isNetWorkGranted) {
            DebugUtil.d(mLogTag, "handleRecordUpgrade, isNetWorkGranted:$isNetWorkGranted")
            return
        }
        AccountManager.sAccountManager.apply {
            // 3.重刷新账号信息
            reqSignInAccount(context, object : IAccountInfoCallback {
                override fun onComplete(account: AccountBean) {
                    DebugUtil.e(mLogTag, " reqSignInAccount onComplete ${account.isLogin}")
                    // 4. 设置拉起登录页
                    sShowLoginPage = true
                    // 5. 记录已处理初次升级上来问题
                    AccountPref.setUpgradeAccountTag(context)

                    val accountInfo = getSignInAccountFromCache(context)
                    if (accountInfo?.ssoid?.isNullOrBlank() == false) {
                        // 已登录：更新账号信息到SP
                        AccountPref.setAccountUId(context, accountInfo.ssoid)
                        TipStatusManager.init(true)
                    } else {
                        // 未登录：关闭云同步开关
                        CloudSynStateHelper.setSyncSwitch(CloudSwitchState.CLOSE, false)
                    }
                }
            })
        }
    }

    /**
     *
     * @param packageName
     * @return 8920
     */
    private fun getAccountAPPVersionCode(context: Context): Long {
        val isRealMe = BaseUtil.isRealme()
        var appVersionCode: Long = 0
        try {
            appVersionCode = context.packageManager?.getPackageInfo(
                if (isRealMe) ACCOUNT_PKG_REAL_ME else ACCOUNT_PKG_OPPO_S, PackageManager.GET_META_DATA)?.longVersionCode ?: 0
            DebugUtil.i(mLogTag, "packageInfo.appVersionCode->$appVersionCode, isRealMe $isRealMe")
        } catch (e: NameNotFoundException) {
            DebugUtil.e(mLogTag, "getAccountAPPVersionCode,isRealMe $isRealMe, $e")
            if (!isRealMe) {
                try {
                    appVersionCode = context.packageManager?.getPackageInfo(ACCOUNT_PKG_OPPO_R, PackageManager.GET_META_DATA)?.longVersionCode ?: 0
                    DebugUtil.i(mLogTag, "packageInfo.appVersionCode->$appVersionCode")
                } catch (e: NameNotFoundException) {
                    DebugUtil.e(mLogTag, "getAccountAPPVersionCode oppoR $e")
                }
            }
        }
        return appVersionCode
    }
}