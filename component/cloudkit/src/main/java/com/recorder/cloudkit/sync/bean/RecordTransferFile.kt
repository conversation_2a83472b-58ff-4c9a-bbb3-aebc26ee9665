package com.recorder.cloudkit.sync.bean

import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.heytap.cloudkit.libsync.service.CloudIOFile
import com.soundrecorder.common.databean.Record

class RecordTransferFile(val record: Record, var cloudIOFile: CloudIOFile) {

    var uploadResult: CloudKitError? = null


    val cloudId: String?
        get() = cloudIOFile.cloudId

    val checkPayload: String?
        get() = cloudIOFile.checkPayload
    val cacheUri: String?
        get() = cloudIOFile.cacheUri

    val uuid: String = record.uuid

    override fun toString(): String {
        return "RecordTransferFile(record=$record, cloudIOFile=$cloudIOFile, uploadResult=$uploadResult)"
    }
}