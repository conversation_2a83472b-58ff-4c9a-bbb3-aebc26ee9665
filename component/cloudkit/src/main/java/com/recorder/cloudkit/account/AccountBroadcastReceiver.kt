/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  AccountBroadcastReceiver.kt
 * * Description : AccountBroadcastReceiver
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.recorder.cloudkit.account

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.recorder.cloudkit.SyncTriggerManager
import com.recorder.cloudkit.global.CloudGlobalStateManager
import com.recorder.cloudkit.push.CloudPushAgent
import com.recorder.cloudkit.sync.CloudSynStateHelper
import com.recorder.cloudkit.sync.ui.SettingRecordSyncFragment
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.registerReceiverCompat
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.buryingpoint.CloudStaticsUtil
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.db.CloudSyncRecorderDbUtil
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.cloudkit.CloudSwitchState
import com.soundrecorder.modulerouter.utils.Injector

/**
 * 账号退出登录广播通知
 */
class AccountBroadcastReceiver : BroadcastReceiver() {

    private val cloudKitApi by lazy {
        Injector.injectFactory<CloudKitInterface>()
    }

    override fun onReceive(context: Context, intent: Intent) {
        if (PermissionUtils.getNextAction() == PermissionUtils.SHOULD_SHOW_USER_NOTICE) {
            DebugUtil.i(TAG, "onReceive: UserNotice is not agree!")
            return
        }

        val action = intent.action
        DebugUtil.i(TAG, "onReceive action $action")
        if (ACTION_ACCOUNT_LOGOUT == action && !CloudSynStateHelper.isLoginFromCache()) {
            val isNetWorkGranted: Boolean = cloudKitApi?.isNetWorkGranted(context) ?: false
            val deleteData = intent.getBooleanExtra(KEY_ACCOUNT_LOGOUT_DATA_CLEAN, intent.getBooleanExtra(KEY_ACCOUNT_LOGOUT_DATA_CLEAN_OLD, false))
            DebugUtil.i(TAG, "onReceive account logout deleteData = $deleteData,netGranted=$isNetWorkGranted")
            if (!isNetWorkGranted) {
                handleLogoutNoneNetOp(context, false, deleteData)
                return
            }
            handleLogoutNoneNetOp(context, true, deleteData)
            //发送刷新界面的广播
            val intentReceiver: Intent = Intent(SettingRecordSyncFragment.ACCOUNT_LOGOUT_RECEIVER)
            LocalBroadcastManager.getInstance(BaseApplication.getAppContext()).sendBroadcast(intentReceiver)

            // 清空recorder.db中同步状态信息、停止同步流程、清除cloudkit 锚点
            SyncTriggerManager.getInstance(BaseApplication.getAppContext()).trigStopSyncForLoginOut(deleteData)

            // 这里不解注册push，退出的时候解注册push
            CloudPushAgent.unregister()
            CloudStaticsUtil.addCloudLog(TAG, "onReceive,LOGOUT,deleteData=$deleteData")
        }
    }

    private fun handleLogoutNoneNetOp(context: Context, isNetGranted: Boolean, deleteData: Boolean) {
        CloudGlobalStateManager.reset()
        SyncTriggerManager.getInstance(BaseApplication.getAppContext()).scheduleWorkerJob({ // 关闭开关,不上报，否则录音进程启动，但是未同意用户须知，会拉起receiver有联网请求
            CloudSynStateHelper.setSyncSwitch(CloudSwitchState.CLOSE, false)
            AccountPref.setAccountUId(BaseApplication.getAppContext(), "")
            if (!isNetGranted) {
                CloudSyncRecorderDbUtil.handleSyncDataForLoginout(context, deleteData)
            }
        }, 0)
    }

    companion object {
        private val TAG: String = AccountBroadcastReceiver::class.java.simpleName
        private const val ACTION_ACCOUNT_LOGOUT = "com.heytap.usercenter.account_logout"

        /*是否删除本机数据key，对应value是布尔值boolean*/
        private const val KEY_ACCOUNT_LOGOUT_DATA_CLEAN = "com.heytap.usercenter.clean_data"
        private const val KEY_ACCOUNT_LOGOUT_DATA_CLEAN_OLD = "com.oppo.usercenter.clean_data"

        fun register(application: Context) {
            val receiver = AccountBroadcastReceiver()
            application.registerReceiverCompat(
                receiver, IntentFilter(ACTION_ACCOUNT_LOGOUT),
                Constants.PERMISSION_OPPO_SAFE_PRIVATE, null, Context.RECEIVER_EXPORTED
            )
        }
    }
}
