/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CloudGroupInfoField.kt
 * * Description : 对应元数据 SDK中CloudMetaDataRecord.java 中fields 字段
 * * Version     : 1.0
 * * Date        : 2025/01/22
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/

package com.recorder.cloudkit.sync.bean

import com.soundrecorder.common.databean.GroupInfo

class CloudGroupInfoField {
    var groupUuid: String? = null
    var groupName: String? = null
    var groupColor: Int = 0
    var groupType: Int = 0

    fun toGroupInfo(): GroupInfo {
        val groupInfo = GroupInfo()
        groupInfo.mUuId = groupUuid.toString()
        groupInfo.mGroupName = groupName.toString()
        groupInfo.mGroupColor = groupColor
        groupInfo.mGroupType = groupType
        return groupInfo
    }

    companion object {
        @JvmStatic
        fun toMetaData(groupInfo: GroupInfo): CloudGroupInfoField = CloudGroupInfoField().apply {
            groupUuid = groupInfo.mUuId
            groupColor = groupInfo.mGroupColor
            groupName = groupInfo.mGroupName
            groupType = groupInfo.mGroupType
        }
    }
}