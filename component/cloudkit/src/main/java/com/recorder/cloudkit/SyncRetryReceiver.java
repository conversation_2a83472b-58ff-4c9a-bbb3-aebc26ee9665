/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: SyncRetryReceiver
 ** Description:
 ** Version: 1.0
 ** Date : 2019-07-11
 ** Author: huangyuanwang
 **
 ** v1.0, 2019-3-12, huangyuanwang, create
 ****************************************************************/
package com.recorder.cloudkit;

import static com.recorder.cloudkit.SyncTriggerManager.RECOVERY_FROM_START_APP;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import java.text.SimpleDateFormat;

import com.soundrecorder.common.db.RecorderDBUtil;

import com.soundrecorder.base.utils.DebugUtil;

public class SyncRetryReceiver extends BroadcastReceiver {

    private static final String TAG = "SyncRetryReceiver";
    public static final String RETRY_SYNC_ACTION = "com.multimedia.newsoundrecorder.action.RETRY_SYNC";
    public static final String CLEAR_FAILED_ACTION = "com.multimedia.newsoundrecorder.action.CLEAR_FAILED_ACTION";
    public static final String EXTRA_RETRY_TYPE = "retry_type";

    private static final int CLEAR_TASK_DELAY = 10 * 1000;

    @Override
    public void onReceive(final Context context, Intent intent) {
        SyncTriggerManager syncTriggerManager = SyncTriggerManager.getInstance(context);
        String action = intent.getAction();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        long currentTime = System.currentTimeMillis();
        DebugUtil.i(TAG, "receive action " + action + ", time is " + df.format(currentTime), true);
        if ((action != null) && action.equalsIgnoreCase(RETRY_SYNC_ACTION)) {
            int retryType = intent.getIntExtra(EXTRA_RETRY_TYPE, -1);
            switch (retryType) {
                case SyncTriggerManager.BACKUP:
                    syncTriggerManager.trigBackupNow();
                    break;
                case SyncTriggerManager.RECOVERY:
                    syncTriggerManager.trigRecoveryNow(RECOVERY_FROM_START_APP);
                    break;
                default:
                    DebugUtil.i(TAG, "not deal type: " + retryType, true);
                    break;
            }
        } else if ((action != null) && action.equalsIgnoreCase(CLEAR_FAILED_ACTION)) {
            syncTriggerManager.scheduleWorkerJob(new Runnable() {
                @Override
                public void run() {
                    DebugUtil.i(TAG, "clearFailedCount ", true);
                    RecorderDBUtil.getInstance(context).clearFailedCount(-1);
                }
            }, CLEAR_TASK_DELAY);
        }
    }
}
