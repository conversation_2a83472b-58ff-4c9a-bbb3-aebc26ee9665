package com.recorder.cloudkit.oldcompat;

import android.app.Activity;
import android.app.Dialog;
import android.content.res.Configuration;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import com.recorder.cloudkit.utils.CloudPermissionUtils;
import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.ToastManager;
import com.soundrecorder.common.permission.PermissionDialogUtils;
import com.soundrecorder.common.permission.PermissionUtils;
import java.util.ArrayList;
import java.util.Collections;

public class CloudPermissionActivity extends AppCompatActivity implements PermissionDialogUtils.PermissionDialogListener {
    private Dialog mPermissionDialog = null;
    private boolean hasClickOnOk = false;

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        showPermissionDialog();
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        overridePendingTransition(0, 0);
    }

    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putBoolean("hasClickOnOk", hasClickOnOk);
    }

    @Override
    protected void onRestoreInstanceState(@NonNull Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);
        hasClickOnOk = savedInstanceState.getBoolean("hasClickOnOk", false);
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        if (hasFocus) {
            if (CloudPermissionUtils.hasCloudRequirePermission()) {
                setResult(Activity.RESULT_OK);
                finish();
            } else {
                if (hasClickOnOk) {
                    if (!(mPermissionDialog != null && mPermissionDialog.isShowing())) {
                        onBackPress(PermissionDialogUtils.TYPE_PERMISSION_ALL_FILE_ACCESS);
                    }
                }
            }
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (CloudPermissionUtils.hasCloudRequirePermission()) {
            setResult(Activity.RESULT_OK);
            finish();
        } else {
            if (hasClickOnOk) {
                onBackPress(PermissionDialogUtils.TYPE_PERMISSION_ALL_FILE_ACCESS);
            } else {
                showPermissionDialog();
            }
        }
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mPermissionDialog != null && mPermissionDialog.isShowing()) {
            mPermissionDialog.dismiss();
        }
        mPermissionDialog = null;
    }

    @Override
    public void onClick(int alertType, boolean isOk, ArrayList<String> permissions) {
        hasClickOnOk = isOk;
        if (isOk) {
            if (BaseUtil.isAndroidROrLater()) {
                PermissionUtils.goToAppAllFileAccessConfigurePermissions(this);
            } else {
                PermissionUtils.goToAppSettingConfigurePermissions(this, permissions);
            }
        } else {
            onBackPress(alertType);
        }
    }

    @Override
    public void onBackPress(int alertType) {
        ToastManager.showLongToast(com.soundrecorder.common.R.string.all_file_access_dialog_toast);
        setResult(Activity.RESULT_CANCELED);
        finish();
    }

    private void showPermissionDialog() {
        // show Dialog时，先取消掉正在显示的Dialog，避免新弹出的Dialog在点击非取消按钮后，还会出现上一次的Dialog
        if (mPermissionDialog != null && mPermissionDialog.isShowing()) {
            mPermissionDialog.dismiss();
        }
        if (BaseUtil.isAndroidROrLater()) {
            mPermissionDialog = PermissionDialogUtils.showPermissionAllFileAccessDialog(this, this);
        } else {
            ArrayList<String> permissions = new ArrayList<>(PermissionUtils.STORAGE_PERMISSIONS_Q.length);
            Collections.addAll(permissions, PermissionUtils.STORAGE_PERMISSIONS_Q);
            mPermissionDialog = PermissionDialogUtils.showReadStoragePermissionDialog(this, this, PermissionDialogUtils.TYPE_DIALOG_DEFAULT,
                permissions);
        }
    }

    @Override
    public void finish() {
        super.finish();
        overridePendingTransition(0, 0);
    }

    @Override
    public void dialogPermissionType(int dialogPermissionType) {
        // do nothing
    }
}
