package com.recorder.cloudkit.account;

import android.content.Context;

import androidx.annotation.NonNull;

import com.heytap.cloudkit.libcommon.account.CloudAccount;
import com.heytap.cloudkit.libcommon.account.ICloudAccountAgent;
import com.heytap.cloudkit.libcommon.account.ICloudAccountCallback;
import com.heytap.usercenter.accountsdk.AccountAgent;
import com.heytap.usercenter.accountsdk.http.AccountNameTask;
import com.heytap.usercenter.accountsdk.model.AccountEntity;
import com.heytap.usercenter.accountsdk.model.BasicUserInfo;
import com.heytap.usercenter.accountsdk.model.SignInAccount;
import com.soundrecorder.base.utils.DebugUtil;

/**
 * 实现cloudkit 接口，cloudkit初始化需要传入此类
 */
public class CloudAccountAgentImpl implements ICloudAccountAgent {
    private static final String TAG = CloudAccountAgentImpl.class.getSimpleName();
    private final Context mContext;

    public CloudAccountAgentImpl(Context context) {
        mContext = context.getApplicationContext();
    }


    @Override
    public void getSignInAccount(@NonNull ICloudAccountCallback callback) {
        callback.onComplete(convertToCloudAccount(AccountAgent.getAccountEntity(mContext, AccountManager.APP_CODE)));
//        AccountAgent.getSignInAccount(mContext, AccountManager.APP_CODE, new OnReqAccountCallbackImpl(callback));
    }

    @Override
    public void reqSignInAccount(@NonNull ICloudAccountCallback callback) {
        AccountAgent.reqSignInAccount(mContext, AccountManager.APP_CODE, new OnReqAccountCallbackImpl(callback));
    }

    @Override
    public void refreshTokenWhenTokenExpire(@NonNull ICloudAccountCallback callback) {
        //云服务SDK新增的token续期的兜底逻辑。与云同步确认，账号SDK本身会续期token，业务无需处理。
    }

    @Override
    public com.heytap.cloudkit.libcommon.provider.LogoutDeleteDataResult deleteSyncDataByLogout(boolean isDeleteData) {
        return null;
    }

    private CloudAccount convertToCloudAccount(AccountEntity accountEntity) {
        CloudAccount cloudAccount = new CloudAccount();
        if (accountEntity == null) {
            cloudAccount.setLogin(false);
            return cloudAccount;
        }
        cloudAccount.setLogin(true);
        cloudAccount.setToken(accountEntity.authToken);
        cloudAccount.setUsername(accountEntity.accountName);
        cloudAccount.setUserId(accountEntity.ssoid);
        return cloudAccount;
    }

    private static class OnReqAccountCallbackImpl implements AccountNameTask.onReqAccountCallback<SignInAccount> {

        private final ICloudAccountCallback callback;

        public OnReqAccountCallbackImpl(ICloudAccountCallback callback) {
            this.callback = callback;
        }

        @Override
        public void onReqStart() {

        }

        @Override
        public void onReqLoading() {

        }

        @Override
        public void onReqFinish(SignInAccount signInAccount) {
            DebugUtil.i(TAG, "onReqFinish signInAccount " + signInAccount.toString(), true);
            CloudAccount account = new CloudAccount();
            account.setToken(signInAccount.token);
            account.setLogin(signInAccount.isLogin);
            account.setResultCode(signInAccount.resultCode);
            account.setResultMsg(signInAccount.resultMsg);
            BasicUserInfo userInfo = signInAccount.userInfo;
            if (null != userInfo) {
                account.setUserId(userInfo.ssoid);
                account.setUsername(userInfo.userName);
                account.setAvatar(userInfo.avatarUrl);
                account.setStatus(userInfo.status);
            }
            callback.onComplete(account);
        }
    }
}
