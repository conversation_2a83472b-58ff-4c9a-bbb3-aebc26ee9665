/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: LoadingSwitchPreference
 * Description:
 * Version: 1.0
 * Date: 2024/12/5
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/12/5 1.0 create
 */

package com.recorder.cloudkit.view

import android.content.Context
import android.util.AttributeSet
import android.view.View
import com.coui.appcompat.preference.COUISwitchLoadingPreference

/**
 * 云同步开关页面一级开关
 * 为了解决开关中点击不立即显示loading状态
 */
open class LoadingSwitchPreference(
    context: Context?,
    attrs: AttributeSet?
) : COUISwitchLoadingPreference(context, attrs) {

    var listener: View.OnClickListener? = null

    override fun onClick() {
        listener?.onClick(switch)
    }

    fun setClickListener(listener: View.OnClickListener) {
        this.listener = listener
    }
}