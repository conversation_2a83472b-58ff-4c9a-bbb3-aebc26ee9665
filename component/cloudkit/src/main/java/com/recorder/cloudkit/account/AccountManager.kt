package com.recorder.cloudkit.account

import android.content.Context
import com.heytap.usercenter.accountsdk.AccountAgent
import com.heytap.usercenter.accountsdk.model.SignInAccount
import com.heytap.usercenter.accountsdk.http.AccountNameTask.onReqAccountCallback
import com.heytap.usercenter.accountsdk.model.AccountEntity
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.PrefUtil
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit

class AccountManager {
    companion object {
        const val APP_CODE = "20007"
        const val TAG = "CloudAccountManager"


        @JvmStatic
        val sAccountManager = AccountManager()

        @JvmStatic
        fun getInstance() = sAccountManager
    }

    private var mCountDownLatch: CountDownLatch? = null

    private constructor() {}

    /**
     * 从账号SDK或我的OPPO获取缓存账号信息
     * 注意：老录音未接入账号SDK升级上来获取登录值是错误的，必须通过AccountCheckHelper.handleRecordUpgrade 处理后才能获取到正确的值
     */
    fun getSignInAccountFromCache(context: Context): AccountEntity? = AccountAgent.getAccountEntity(context, APP_CODE)

    /**
     * 获取登录信息
     * 有三类情况会走网络请求：无缓存，缓存和我的oppo数据有不同，设置为前台且缓存时间超过分钟，
     */
    fun getSignInAccount(context: Context?, callback: IAccountInfoCallback?) {
        AccountAgent.getSignInAccount(context, APP_CODE, OnAccountReqCallbackImpl(callback))
    }

    /**
     * 请求登录
     */
    fun reqSignInAccount(context: Context?, callback: IAccountInfoCallback?) {
        AccountAgent.reqSignInAccount(context, APP_CODE, OnAccountReqCallbackImpl(callback))
    }

    /**
     * check登录状态
     *
     * AccountAgent.isLogin(context, APP_CODE)  方法获取的缓存信息，不准确
     * 注意：老录音未接入账号SDK升级上来获取登录值是错误的，必须通过AccountCheckHelper.handleRecordUpgrade 处理后才能获取到正确的值
     */
    @Synchronized
    fun isLogin(context: Context?): Boolean {
        mCountDownLatch = CountDownLatch(1)
        var login = false
        getSignInAccount(context, object : IAccountInfoCallback {
            override fun onComplete(account: AccountBean) {
                login = account.isLogin
                mCountDownLatch?.countDown()
            }
        })
        try {
            val await = mCountDownLatch?.await(1, TimeUnit.SECONDS)
            DebugUtil.i(TAG, "await result $await", true)
        } catch (e: InterruptedException) {
            DebugUtil.e(TAG, "getSignInAccount error $e")
        }
        DebugUtil.e(TAG, "login state : $login")
        return login
    }


    /**
     * 检查是否需要进行二次校验
     */
    fun checkAccountIsNeedVerify(): Boolean {
        /*
        val context = BaseApplication.getAppContext()
        // 需要缓存ssoid
        val storedId = PrefUtil.getSharedPreferences(context).getString(PrefUtil.KEY_VERIFIED_ACCOUNT_SSOID, "")
        DebugUtil.d(TAG, "checkAccountIsVerified, storedId: $storedId")
        if (storedId.isNullOrEmpty()) {
           return true
        } else {
            val cacheId =  getLoginIdFromCache(context)
            DebugUtil.d(TAG, "checkAccountIsVerified, cacheId: $cacheId")
            return storedId != cacheId //校验后会缓存ssoid， ssoid如果发生变化，才会再次校验
        }
        */
         //add by tangjl 测试环境不要验证
        return false
    }


    fun isLoginFromCache(context: Context): Boolean {
        val accountEntity = getSignInAccountFromCache(context)
        DebugUtil.i(TAG, "isLoginFromCache $accountEntity")
        return accountEntity?.ssoid?.isBlank()?.not() ?: false
    }

    fun getLoginIdFromCache(context: Context): String {
        val accountEntity = getSignInAccountFromCache(context)
        DebugUtil.i(TAG, "getLoginIdFromCache:$accountEntity")
        return accountEntity?.ssoid ?: ""
    }

    fun checkUserIdChanged(context: Context, userId: String): Boolean {
        val oldId = AccountPref.getAccountUId(context)
        return oldId != userId
    }

    fun saveUserId(context: Context, userId: String) {
        AccountPref.setAccountUId(context, userId)
    }

    inner class OnAccountReqCallbackImpl(val callback: IAccountInfoCallback?) :
        onReqAccountCallback<SignInAccount> {
        override fun onReqStart() {}
        override fun onReqLoading() {}
        override fun onReqFinish(signInAccount: SignInAccount) {
            DebugUtil.i(TAG, "onReqFinish signInAccount.id ${signInAccount.userInfo?.ssoid}", true)
            val account = AccountBean(signInAccount.isLogin).apply {
                val userInfo = signInAccount.userInfo
                if (null != userInfo) {
                    userId = userInfo.ssoid
                }
            }
            callback?.onComplete(account)
        }
    }
}