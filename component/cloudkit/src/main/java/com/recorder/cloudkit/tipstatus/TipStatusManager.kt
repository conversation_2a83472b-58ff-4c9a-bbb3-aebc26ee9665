package com.recorder.cloudkit.tipstatus

import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.SharedPreferences
import android.net.ConnectivityManager
import android.os.Handler
import android.os.Looper
import android.os.Message
import androidx.lifecycle.MutableLiveData
import com.heytap.cloud.sdk.base.CloudJumpHelper
import com.heytap.cloudkit.libsync.cloudswitch.bean.SwitchState
import com.recorder.cloudkit.SyncTriggerManager
import com.recorder.cloudkit.account.AccountCheckHelper
import com.recorder.cloudkit.account.AccountManager
import com.recorder.cloudkit.account.AccountPref
import com.recorder.cloudkit.global.CloudGlobalStateManager
import com.recorder.cloudkit.push.CloudPushAgent
import com.recorder.cloudkit.recevier.PowerConnectionReceiver
import com.recorder.cloudkit.sync.CloudSynStateHelper
import com.recorder.cloudkit.sync.CloudSynStateHelper.isSupportSwitch
import com.recorder.cloudkit.sync.RecordSyncChecker
import com.recorder.cloudkit.sync.bean.CloudSyncResult
import com.recorder.cloudkit.sync.bean.CloudSyncResult.Companion.ERROR_FROM_METADATA_BACKUP
import com.recorder.cloudkit.sync.bean.CloudSyncResult.Companion.ERROR_FROM_METADATA_RECOVERY
import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode.CODE_ACCOUNT_DISABLE
import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode.CODE_DEVICE_DISABLE
import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode.RESULT_CANCEL
import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode.RESULT_INSUFFICIENT_SPACE
import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode.RESULT_LOCAL_INSUFFICIENT_SPACE
import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode.RESULT_LOW_BATTERY
import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode.RESULT_LOW_BATTERY_CHARGING
import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode.RESULT_NETWORK_NO_CONNECT
import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode.RESULT_NETWORK_TYPE_MISMATCH
import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode.RESULT_POWER_SAVING_MODE
import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode.RESULT_SUCCESS
import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode.RESULT_TEMPERATURE_HIGH
import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode.UI_STATE_QUERYING
import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode.UI_STATE_SYNCING
import com.recorder.cloudkit.sync.listener.NotifyDialogListener
import com.recorder.cloudkit.sync.ui.SettingRecordSyncActivity
import com.recorder.cloudkit.tipstatus.dialog.NetWorkStatusRecevier
import com.recorder.cloudkit.utils.CloudPermissionUtils
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NetworkUtils
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.common.buryingpoint.CloudStaticsUtil
import com.soundrecorder.common.db.RecorderDBUtil
import com.soundrecorder.common.flexible.FollowHandPanelUtils
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.utils.RecordFileChangeNotify
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.cloudkit.CloudSwitchState
import com.soundrecorder.modulerouter.cloudkit.ICloudGlobalStateCallBack
import com.soundrecorder.modulerouter.cloudkit.tipstatus.CloudGuideState
import com.soundrecorder.modulerouter.cloudkit.tipstatus.ICloudSwitchChangeListener
import com.soundrecorder.modulerouter.cloudkit.tipstatus.ITipStatus
import com.soundrecorder.modulerouter.utils.Injector
import java.util.Calendar
import java.util.concurrent.CopyOnWriteArrayList

object TipStatusManager : CloudSwitchStatusHelper.CloudSwitchStateChangeListener {
    private const val GUIDE_SHOW_AGAIN_MONTH_TIME = 3
    const val RECORD_MODULE = "record"

    private const val TAG = "TipStatusManager"
    private const val STATUS_INIT = -1
    private const val DELAY_TIME_HIDE_STATUS = 4000L
    private const val DELAY_TIME_HIDE_STATUS_FAILURE = 7000L
    private const val DELAY_TIME_TRIG_SYNC = 200L

    /**
     * 隐藏副标题同步状态
     */
    const val MSG_HIDE_STATUS = 213

    @JvmStatic
    var syncResult: Int = STATUS_INIT

    @JvmStatic
    var mSyncResultData: CloudSyncResult? = null

    //these effect tip status
    @JvmStatic
    var needSyncCount: Int = 0

    @JvmStatic
    var needSyncBackUpCount: Int = 0

    @JvmStatic
    var syncSwitch: Int = -1

    @JvmStatic
    var login: Boolean = false

    /**
     * 该区域是否支持云同步，cloudkitSdk提供，目前内销+外销10个国家支持
     */
    private var isSupportedCloud: Boolean = false

    @JvmStatic
    var hasCloudRequiredPermissions: Boolean = false

    @JvmStatic
    val tipStatusLiveData = MutableLiveData<ITipStatus>(TipStatus.NONE)

    /*云同步开关状态变化监听*/
    private var mCloudSwitchStatusHelper: CloudSwitchStatusHelper? = null

    @JvmStatic
    var mNotifyDialogListener: NotifyDialogListener? = null
        private set

    private var mPowerConnectionReceiver: PowerConnectionReceiver? = null
    private var mReceiver: NetWorkStatusRecevier? = null

    val ABNORMAL_STOP_SYNC_CODES = arrayOf(
        RESULT_NETWORK_TYPE_MISMATCH,
        RESULT_LOCAL_INSUFFICIENT_SPACE,
        RESULT_TEMPERATURE_HIGH,
        RESULT_POWER_SAVING_MODE
    )

    private val mHandler: Handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            when (msg.arg1) {
                MSG_HIDE_STATUS -> tipStatusLiveData.postValue(TipStatus.NONE)
            }
        }
    }

    /*对外，如业务层，提供的 云同步开关，开-关，关-开的变化listener*/
    private val mCloudListenerSet: CopyOnWriteArrayList<ICloudSwitchChangeListener> = CopyOnWriteArrayList<ICloudSwitchChangeListener>()

    private val STATUS_CLOUD_SYNC = arrayOf(
        TipStatus.NO_ALL_ACCESS_PERMISSION,
        TipStatus.FAILURE,
        TipStatus.QUERY,
        TipStatus.SYNCING,
        TipStatus.COMPLETED
    )

    private val cloudKitApi by lazy {
        Injector.injectFactory<CloudKitInterface>()
    }

    fun init(fromCheckAccount: Boolean = false) {
        // 处理重建
        tipStatusLiveData.value = tipStatusLiveData.value ?: TipStatus.NONE
        isSupportedCloud = CloudSynStateHelper.isRegionCloudSupport()
        DebugUtil.i(TAG, "init  isSupportedCloud  $isSupportedCloud")
        if (!isSupportedCloud) {
            CloudStaticsUtil.addCloudLog(TAG, "init,not support cloud, mainSystem ${BaseApplication.sIsMainSystem}")
            return
        }

        initCloudSwitchChangeListener()
        // 支持云同步才做以下操作
        val appContext = BaseApplication.getAppContext()
        SyncTriggerManager.getInstance(appContext).scheduleWorkerJob({
            DebugUtil.i(TAG, " init start.....")
            AccountCheckHelper.getInstance().checkAccount(BaseApplication.getAppContext())

            // 获取登录状态
            login = CloudSynStateHelper.isLoginFromCache()
            initCloudGlobalState()
            // 使用过CK版本了，需要check登录状态；未使用过CK版本，则不需要check登录状态（老版本录音未接入账号SDK,升级上来账号SDK获取到的用户信息为null，导致登录状态判断不准确）
            syncSwitch = CloudSynStateHelper.getSwitchState(false)
            needSyncCount = RecorderDBUtil.getInstance(BaseApplication.getAppContext()).needSyncCount
            notifyCloudToggleChange(true, syncSwitch > CloudSwitchState.CLOSE)
            CloudStaticsUtil.addCloudLog(TAG, "init, login=$login, syncSwitch=$syncSwitch, needSyncCount=$needSyncCount")
            // 处理已登录设备，从未接入账号SDK版本升级上来，未执行checkAccount导致获取到的登录状态为false，check成功后，若需要显示引导卡片，则刷新首页数据（exp-rsa需求未登录不显示卡片）
            if (fromCheckAccount && BaseUtil.isExpRSA4() && isNeedShowGuide()) {
                DebugUtil.d(TAG, "refresh record data")
                val intent = Intent(RecordFileChangeNotify.FILE_UPDATE_ACTION)
                BaseUtil.sendLocalBroadcast(BaseApplication.getAppContext(), intent)
            }
        }, 0)
    }

    private fun initCloudGlobalState() {
        cloudKitApi?.initCloudGlobalState(object : ICloudGlobalStateCallBack {
            override fun onSuccess(changed: Boolean, support: Boolean, state: String?) {
                if (changed) {
                    val intent = Intent(RecordFileChangeNotify.FILE_UPDATE_ACTION)
                    BaseUtil.sendLocalBroadcast(BaseApplication.getAppContext(), intent)
                }
            }
        })
    }


    private fun setSyncSwitchState(switchState: Int) {
        val lastState = syncSwitch
        DebugUtil.i(TAG, "syncSwitch  $lastState >>> $switchState")
        if (lastState != switchState) {
            syncSwitch = switchState
            if ((lastState <= CloudSwitchState.CLOSE) && (switchState > CloudSwitchState.CLOSE)) {
                // 0 >>> 1,2 open true
                updateLoginState()
                notifyCloudToggleChange(false, true)
                // 打开云同步，注册push
                CloudPushAgent.checkPushRegister(BaseApplication.getAppContext())
            } else if ((lastState > CloudSwitchState.CLOSE) && (switchState <= CloudSwitchState.CLOSE)) {
                //1,2 >>> 0 open false
                updateLoginState()
                notifyCloudToggleChange(false, false)
                executeHideStatus(0)
                // 关闭云同步，注销push注册
                CloudPushAgent.unregister()
            }
            //other 1 >>> 2 or 2 >>> 1 do nothing
        }
    }

    private fun updateLoginState() {
        login = CloudSynStateHelper.isLoginFromCache()
        if (login) {
            AccountPref.setAccountUId(BaseApplication.getAppContext(),
                AccountManager.sAccountManager.getLoginIdFromCache(BaseApplication.getAppContext()))
        }
    }

    /**
     * 监听云同步开关变化
     */
    private fun initCloudSwitchChangeListener() {
        if (mCloudSwitchStatusHelper == null) {
            mCloudSwitchStatusHelper = CloudSwitchStatusHelper(BaseApplication.getAppContext())
            mCloudSwitchStatusHelper?.addCloudSwitchChangeListener(this)
            mCloudSwitchStatusHelper?.registerSwitchObserver()
        }
    }

    /**
     * 通过TipManager对外暴露 云同步开关发生变化listener（非录音改变）
     */
    fun addCloudSwitchChangeListener(listener: CloudSwitchStatusHelper.CloudSwitchStateChangeListener) {
        if (mCloudSwitchStatusHelper != null) {
            mCloudSwitchStatusHelper?.addCloudSwitchChangeListener(listener)
        }
    }

    fun removeCloudSwitchChangeListener(listener: CloudSwitchStatusHelper.CloudSwitchStateChangeListener) {
        if (mCloudSwitchStatusHelper != null) {
            mCloudSwitchStatusHelper?.removeCloudSwitchChangeListener(listener)
        }
    }

    fun addNotifyDialogListener(listener: NotifyDialogListener) {
        if (mNotifyDialogListener == null) {
            mNotifyDialogListener = listener
        }
    }

    fun releaseNotifyDialogListener() {
        if (mNotifyDialogListener != null) {
            mNotifyDialogListener = null
        }
    }

    fun resetTipsStatus() {
        executeHideStatus(0)
    }

    fun getTipStatus(): TipStatus {
        // 先移除隐藏副标题的消息
        removeHideStatusMsg()
        if (!isSupportedCloud) {
            DebugUtil.i(TAG, "getTipStatus  isSupportedCloud $isSupportedCloud")
            return TipStatus.NONE
        }
        // 同步过程中的 token过期，需要抛给UI
        if ((!login && syncResult == STATUS_INIT) || (syncSwitch == CloudSwitchState.CLOSE)) {
            return TipStatus.CLOUD_OFF
        }
        /**
         * quey状态提前
         * 云同步打开的媒体库全量对比逻辑放在同步过程中的，
         * （query状态包含媒体库全量对比+查询第一页云数据）
         * 媒体库全量对比时+无所有文件管理权限，会抛无权限给UI层，
         * 此时同步handerThread再执行上一次同步message，导致用户打开权限时同步handerThread队列等待
         * */
        if (syncResult == UI_STATE_QUERYING) {
            return TipStatus.QUERY
        }
        if (!hasCloudRequiredPermissions) {
            return TipStatus.NO_ALL_ACCESS_PERMISSION
        }
        return when (syncResult) {
            STATUS_INIT -> TipStatus.NONE
            RESULT_CANCEL -> TipStatus.NONE
            CODE_ACCOUNT_DISABLE, CODE_DEVICE_DISABLE -> TipStatus.NONE
            UI_STATE_QUERYING -> TipStatus.QUERY
            UI_STATE_SYNCING -> TipStatus.SYNCING
            // 云服务同步成功的，展示4S
            RESULT_SUCCESS -> {
                // 本地有待同步的数据，且全部非备份数据
                if ((mSyncResultData?.enterByRecovery == true) && (needSyncCount > 0) && (needSyncCount != needSyncBackUpCount)) {
                    // 完整的同步流程，本地有文件失败了
                    mSyncResultData?.errorFrom = ERROR_FROM_METADATA_RECOVERY
                    syncResult = mSyncResultData?.errorCode ?: 0
                    return TipStatus.FAILURE
                } else if (needSyncBackUpCount > 0) {
                    mSyncResultData?.errorFrom = ERROR_FROM_METADATA_BACKUP
                    syncResult = mSyncResultData?.errorCode ?: 0
                    // 备份流程，本地有未备份成功的文件
                    return TipStatus.FAILURE
                }
                // 流程对应数据全部成功
                executeHideStatus(DELAY_TIME_HIDE_STATUS)
                TipStatus.COMPLETED
            }
            // 云空间已满，展示 7s
            RESULT_INSUFFICIENT_SPACE -> {
                executeHideStatus(DELAY_TIME_HIDE_STATUS_FAILURE)
                DebugUtil.i(TAG, "TipStatus RESULT_INSUFFICIENT_SPACE")
                CloudStaticsUtil.addCloudTipsUpgradeSpacePopEvent()
                TipStatus.NO_CLOUD_SPACE
            }
            // 同步暂停的，一直展示
            else -> TipStatus.FAILURE
        }
    }

    private fun executeHideStatus(delayMill: Long) {
        DebugUtil.i(TAG, "executeHideStatus delay: $delayMill ")
        removeHideStatusMsg()
        val message = mHandler.obtainMessage()
        message.what = MSG_HIDE_STATUS
        message.arg1 = MSG_HIDE_STATUS
        mHandler.sendMessageDelayed(message, delayMill)
    }

    /**
     * 移除隐藏副标题的消息
     */
    private fun removeHideStatusMsg() {
        mHandler.removeMessages(MSG_HIDE_STATUS)
    }

    @JvmStatic
    fun updateSyncResult(cloudSyncResult: CloudSyncResult) {
        DebugUtil.i(TAG, "updateSyncResult  $cloudSyncResult")
        val context = BaseApplication.getAppContext()
        hasCloudRequiredPermissions = CloudPermissionUtils.hasCloudRequirePermission()
        login = CloudSynStateHelper.isLoginFromCache()
        needSyncCount = RecorderDBUtil.getInstance(context).needSyncCount
        needSyncBackUpCount = RecorderDBUtil.getInstance(context).needBackUpCount
        if (cloudSyncResult.isSuccess()) {
            // 本次同步为recovery，同步过程总，有部分文件下载失败(可重试错误码)，改变error状态
            if ((cloudSyncResult.enterByRecovery) && (needSyncCount > 0)) {
                cloudSyncResult.errorCode = cloudSyncResult.errorCode
                cloudSyncResult.errorFrom = CloudSyncResult.ERROR_FROM_FILE_RECOVERY
            }
            // 本次同步为backUp，同步过程总，有部分文件上传失败(可重试错误码)，改变error状态
            if ((!cloudSyncResult.enterByRecovery) && (needSyncBackUpCount > 0)) {
                cloudSyncResult.errorCode = cloudSyncResult.errorCode
                cloudSyncResult.errorFrom = CloudSyncResult.ERROR_FROM_FILE_BACKUP
            }
        }
        mSyncResultData = cloudSyncResult
        syncResult = cloudSyncResult.errorCode
        val status = getTipStatus()
        DebugUtil.i(TAG, "updateSyncResult-status  $status, $this")
        tipStatusLiveData.postValue(status)
    }

    @JvmStatic
    fun isCloudOn(): Boolean {
        return isSupportedCloud && login && (syncSwitch > CloudSwitchState.CLOSE)
    }

    fun isRefreshUIOfSync(): Boolean = tipStatusLiveData.value in STATUS_CLOUD_SYNC

    fun isSyncing(): Boolean =
        ((tipStatusLiveData.value == TipStatus.QUERY) || (tipStatusLiveData.value == TipStatus.SYNCING))

    /**
     * 跳转云同步开关设置页面
     */
    @JvmStatic
    fun toCloudSetting(context: Context?) {
        if (!isSupportSwitch()) {
            // 老版本，跳转到云同步APP 设置页面
            if (!CloudJumpHelper.jumpModuleSetting(context, RECORD_MODULE)) {
                CloudJumpHelper.jumpMain(context, RECORD_MODULE)
            }
        } else {
            // 新版本跳转到录音自己的页面
            val intent = Intent(context, SettingRecordSyncActivity::class.java)
            context?.let {
                FollowHandPanelUtils.startActivity(it, intent)
            }
        }
    }

    fun checkNeedSyncFullRecovery(): Boolean {
        val appContext = BaseApplication.getAppContext()
        val isTrig = RecordSyncChecker.checkNeedFullRecovery(appContext)
        DebugUtil.i(TAG, "checkNeedSyncFullRecovery  $isTrig")
        if (isTrig) {
            SyncTriggerManager.getInstance(appContext).trigStopSyncForClearAnchor()
            // 触发云端全量恢复前（参数 true）执行一次本地媒体库全量对比，后期如果反馈慢此处可去掉
            SyncTriggerManager.getInstance(appContext).trigRecoveryNow(true)
            RecordSyncChecker.updateFullRecoveryTime(appContext)
        }
        return isTrig
    }

    fun release() {
        DebugUtil.i(TAG, "release is in")
        mCloudSwitchStatusHelper?.removeCloudSwitchChangeListener(this)
        mCloudSwitchStatusHelper?.unregisterSwitchObserver()
        mCloudSwitchStatusHelper = null
        mHandler.removeCallbacksAndMessages(null)
        isSupportedCloud = false
        syncResult = STATUS_INIT
        tipStatusLiveData.value = TipStatus.NONE
        login = false
        syncSwitch = -1
        unRegister()
        releaseNotifyDialogListener()
    }

    /**
     * 云同步开关状态发生变化
     */
    override fun onCloudSwitchChanged(switchState: SwitchState) {
        DebugUtil.i(TAG, "onCloudSwitchChanged $switchState")
        setSyncSwitchState(switchState.state)
        CloudStaticsUtil.addCloudLog(TAG, "onCloudSwitchChanged,change to ${switchState.state}")
    }

    override fun toString(): String {
        return """{
                isSupportedCloud = $isSupportedCloud , 
                hasCloudRequiredPermissions = $hasCloudRequiredPermissions , 
                login = $login , 
                syncSwitch = $syncSwitch , 
                syncResult = $syncResult , 
                needSyncCount = $needSyncCount , 
                needSyncBackUpCount = $needSyncBackUpCount }"""
    }

    private fun notifyCloudToggleChange(isInitValue: Boolean, open: Boolean) {
        DebugUtil.i(TAG, "notifyCloudToggleChange is $open,isInitValue=$isInitValue")
        if (mCloudListenerSet.isNotEmpty()) {
            mHandler.post {
                mCloudListenerSet.iterator().run {
                    while (hasNext()) {
                        next().onToggle(isInitValue, open)
                    }
                }
            }
        }
        // 触发云同步相关操作
        trigCloudSyncForSwitchToggle(isInitValue, open)
        // 更新SP
        if (open) {
            updateGuideState(BaseApplication.getAppContext())
            registerReceiver()
        } else {
            unRegister()
        }
    }

    /**
     * 开发发送变化，触发云同步
     * @param isInitValue true: 初始值 false：开关状态发生变化
     * @param open true：开启 false：关闭
     */
    private fun trigCloudSyncForSwitchToggle(isInitValue: Boolean, open: Boolean) {
        // 正在显示用户须知弹窗过程中，不处理
        if (PermissionUtils.getNextAction() <= PermissionUtils.SHOULD_SHOW_USER_NOTICE) {
            DebugUtil.i(TAG, "notifyCloudToggleChange showing user notice dialog")
            return
        }
        val appContext = BaseApplication.getAppContext()
        if (open) {
            if (isInitValue) {
                // 非第一次打开冷启动触发云同步
                if (CloudPermissionUtils.hasCloudRequirePermission() && RecordSyncChecker.checkLastEnterInOneHour(appContext)) {
                    SyncTriggerManager.getInstance(appContext).trigRecoveryNow(SyncTriggerManager.RECOVERY_FROM_START_APP)
                } else {
                    DebugUtil.e(TAG, "onToggle init trig sync checker failure ")
                }
            } else {
                // 开关打开，全量同步
                SyncTriggerManager.getInstance(appContext).trigRecoveryNow(true, SyncTriggerManager.RECOVERY_FROM_MANUAL)
            }
        } else {
            SyncTriggerManager.getInstance(BaseApplication.getAppContext()).trigStopSyncForLoginOut(false)
        }
    }

    private fun updateGuideState(context: Context) {
        TipStatusManager.run {
            //Observe the cloud synchronization switch to open, update the cloud service guide state
            val guideState = PrefUtil.getInt(context, PrefUtil.KEY_GUIDE_STATE, CloudGuideState.GUIDE_STATE_DEFAULT)
            if ((login)
                && (syncSwitch > CloudSwitchState.CLOSE)
                && (guideState != CloudGuideState.GUIDE_STATE_OPEN)
                && (guideState != CloudGuideState.GUIDE_STATE_IGNORE_AGAIN)
            ) {
                PrefUtil.putInt(context, PrefUtil.KEY_GUIDE_STATE, CloudGuideState.GUIDE_STATE_OPEN)
            }
        }
    }

    fun registerCloudListener(listener: ICloudSwitchChangeListener?) {
        if (listener != null && !mCloudListenerSet.contains(listener)) {
            mCloudListenerSet.add(listener)
        }
        DebugUtil.i(TAG, "registerCloudListener $listener")
    }

    fun unregisterCloudToggleListener(listener: ICloudSwitchChangeListener?) {
        if (mCloudListenerSet.contains(listener)) {
            mCloudListenerSet.remove(listener)
        }
        DebugUtil.i(TAG, "unregisterCloudToggleListener $listener ，remaining amount:${mCloudListenerSet.size} ")
    }

    /**
     * 首页是否支持显示云同步引导卡片
     * 【支持云同步国家 & (未登录状态 || 云同步开关关闭)】
     * 用户首次安装(默认云同步关闭)，显示引导卡片
     * 1. 用户点击忽略，等 3个月后再显示引导卡片
     * 2.用户开启后，再关闭 or 登录 不显示引导卡片
     */
    fun isNeedShowGuide(): Boolean {
        if (!CloudSynStateHelper.isRegionCloudSupport()) {
            //如果不支持云同步，不显示
            return false
        }
        if (!CloudGlobalStateManager.canShowSyncSwitch()) {
            //如果设备or账号不支持云同步，不显示
            return false
        }
        if (BaseUtil.isExpRSA4() && !CloudSynStateHelper.isLoginFromCache()) {
            //外销 RSA4.0 +未登录，不显示卡片
            return false
        }
        val context: Context? = BaseApplication.getAppContext()
        // 云同步开关 打开状态，不显示should_clearUserData_when_onReceiveLoginOut
        if (isCloudOn()) {
            return false
        }
        // 增加这个是为了初次升级CK版本，login为false，会导致卡片显示
        if (syncSwitch > CloudSwitchState.CLOSE) {
            return false
        }
        val pref: SharedPreferences? = PrefUtil.getSharedPreferences(context)
        return when (pref?.getInt(PrefUtil.KEY_GUIDE_STATE, CloudGuideState.GUIDE_STATE_DEFAULT)) {
            CloudGuideState.GUIDE_STATE_DEFAULT -> true
            CloudGuideState.GUIDE_STATE_OPEN -> false
            CloudGuideState.GUIDE_STATE_IGNORE -> isThreeMonthsLater(context)
            CloudGuideState.GUIDE_STATE_IGNORE_AGAIN -> false
            else -> false
        }
    }

    private fun isThreeMonthsLater(context: Context?): Boolean {
        val pref: SharedPreferences? = PrefUtil.getSharedPreferences(context)
        val defaultTime = System.currentTimeMillis()
        val ignoreTime = pref?.getLong(PrefUtil.KEY_GUIDE_IGNORE_TIME, defaultTime) ?: defaultTime
        val now = Calendar.getInstance()
        val threeMonthsLater = Calendar.getInstance()
        threeMonthsLater.timeInMillis = ignoreTime
        threeMonthsLater.add(Calendar.MONTH, GUIDE_SHOW_AGAIN_MONTH_TIME)
        return now.timeInMillis > threeMonthsLater.timeInMillis
    }

    private fun registerReceiver() {
        registerNetWorkStatus()
        registerPowerConnectionReceiver()
    }

    /**
     * 监听充电插拔电量变化
     */
    private fun registerPowerConnectionReceiver() {
        if (mPowerConnectionReceiver == null) {
            DebugUtil.i(TAG, "registerPowerConnectionReceiver")
            mPowerConnectionReceiver = PowerConnectionReceiver {
                if (syncResult == RESULT_LOW_BATTERY || syncResult == RESULT_LOW_BATTERY_CHARGING || syncResult == RESULT_POWER_SAVING_MODE) {
                    if (it != null) {
                        //处于省电模式优先显示省电模式弹窗
                        if (RecordSyncChecker.isPowerSaveMode(BaseApplication.getAppContext())) {
                            syncResult = RESULT_POWER_SAVING_MODE
                            //更新弹窗UI
                            if (mNotifyDialogListener != null) {
                                mNotifyDialogListener?.notifyContent()
                            }
                        } else {
                            val batteryInfo =
                                RecordSyncChecker.getBatteryInfo(BaseApplication.getAppContext())
                            DebugUtil.i(TAG, "get battery info is:$batteryInfo")
                            when (it.action) {
                                Intent.ACTION_POWER_CONNECTED -> {
                                    //  充电：电量小于10%
                                    if (batteryInfo != null) {
                                        if (batteryInfo.batteryLevel < RecordSyncChecker.BATTERY_CHARGING_MIN_TEMP) {
                                            syncResult = RESULT_LOW_BATTERY_CHARGING
                                            if (mNotifyDialogListener != null) {
                                                mNotifyDialogListener?.notifyContent()
                                            }
                                        } else {
                                            //充电下 电量大于10   自动同步
                                            SyncTriggerManager.getInstance(BaseApplication.getAppContext())
                                                .trigRecoveryNow()
                                            if (mNotifyDialogListener != null) {
                                                mNotifyDialogListener?.doDismiss()
                                            }
                                        }
                                    }
                                }
                                Intent.ACTION_POWER_DISCONNECTED -> {
                                    if (batteryInfo != null) {
                                        if (batteryInfo.batteryLevel < RecordSyncChecker.BATTERY_UN_CHARGING_MIN_TEMP) {
                                            syncResult = RESULT_LOW_BATTERY
                                            if (mNotifyDialogListener != null) {
                                                mNotifyDialogListener?.notifyContent()
                                            }
                                        } else {
                                            //当电量大于20  关闭弹窗，恢复默认状态
                                            resetTipsStatus()
                                            if (mNotifyDialogListener != null) {
                                                mNotifyDialogListener?.doDismiss()
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            val intentFilter = IntentFilter()
            intentFilter.addAction(Intent.ACTION_POWER_CONNECTED)
            intentFilter.addAction(Intent.ACTION_POWER_DISCONNECTED)
            BaseApplication.getAppContext().registerReceiver(mPowerConnectionReceiver, intentFilter)
        }
    }

    @Suppress("TooGenericExceptionCaught")
    private fun unRegisterNetWorkStatus() {
        try {
            if (mReceiver != null) {
                BaseApplication.getAppContext().unregisterReceiver(mReceiver)
                mReceiver = null
            }
        } catch (e: Exception) {
            DebugUtil.w(TAG, "unRegisterNetWorkStatus error: $e", false)
            mReceiver = null
        }
    }

    private fun unRegisterPowerConnectionReceiver() {
        if (mPowerConnectionReceiver != null) {
            try {
                BaseApplication.getAppContext().unregisterReceiver(mPowerConnectionReceiver)
                DebugUtil.i(TAG, "unRegisterPowerConnectionReceiver")
            } catch (e: IllegalArgumentException) {
                DebugUtil.w(TAG, "unRegisterPowerConnectionReceiver error: $e", false)
            } finally {
                mPowerConnectionReceiver = null
            }
        }
    }

    /**
     * 监听网络变化，在RESULT_NETWORK_NO_CONNECT状态下，连接上网时，dismiss弹窗
     * 有网络连接关闭弹窗，刷新副标题
     * 根据开关是否允许数据流量决定是否触发云同步
     */
    private fun registerNetWorkStatus() {
        if (mReceiver == null) {
            DebugUtil.i(TAG, "registerNetWorkStatus")
            mReceiver = NetWorkStatusRecevier {
                val netState = NetworkUtils.getNetState(BaseApplication.getAppContext())
                DebugUtil.d(TAG, "NetWorkStatusRecevier changed to $netState ")
                val syncSwitch = syncSwitch
                if (netState != NetworkUtils.NETWORK_NONE) {
                    if (netState == NetworkUtils.NETWORK_WIFI || (netState == NetworkUtils.NETWORK_MOBILE && syncSwitch == SwitchState.OPEN_ALL.state)) {
                        if (syncResult == RESULT_NETWORK_NO_CONNECT || syncResult == RESULT_NETWORK_TYPE_MISMATCH) {
                            SyncTriggerManager.getInstance(BaseApplication.getAppContext())
                                .trigRecoveryNow()
                            if (mNotifyDialogListener != null) {
                                mNotifyDialogListener?.doDismiss()
                            }
                        }
                    }
                } else {
                    SyncTriggerManager.getInstance(BaseApplication.getAppContext()).trigStopSyncForErrorCode(RESULT_NETWORK_NO_CONNECT)
                }
            }
            val intentFilter = IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION)
            BaseApplication.getAppContext().registerReceiver(mReceiver, intentFilter)
        }
    }

    private fun unRegister() {
        unRegisterNetWorkStatus()
        unRegisterPowerConnectionReceiver()
        DebugUtil.i(TAG, "unRegister")
    }
}