package com.recorder.cloudkit.sync.config

import com.google.gson.Gson
import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.heytap.cloudkit.libsync.ext.CloudSyncManager
import com.heytap.cloudkit.libsync.ext.ICloudRecoveryMetaData
import com.heytap.cloudkit.libsync.metadata.helper.CloudRecoveryRequestSource
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudMetaDataRecord
import com.heytap.cloudkit.libsync.service.CloudDataType
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.recorder.cloudkit.sync.CloudSyncAgent
import com.recorder.cloudkit.sync.RecordSyncChecker
import com.recorder.cloudkit.sync.SyncDataConstants
import com.recorder.cloudkit.sync.bean.CloudSyncResult

object CloudConfigFetcher {
    private const val TAG = "CloudConfigFetcher"
    const val RECORD_TYPE_CONFIG = "recordConfig"
    private var mGson: Gson = Gson()


    @JvmStatic
    fun getCloudConfig(callBack: ConfigCallback) {
        val dataList: MutableList<CloudConfigBean> = mutableListOf()
        getCloudConfigImpl(dataList, callBack)
    }

    /**
     * cloudDataType：CloudDataType.PUBLIC
     * sysRecordType='recordConfig'
     * fields='{"description":"查看云端数据","region":["SG","IN","ALL"],
     * "value":"{\"linkUrl\":\"http://sg-ocloud-webv2-test.wanyol.com\"，\"type\":\"h5\",\"order\":1}",
     * "version":"1.0",
     * "key":"record.ocloudH5"}'
     */
    private fun getCloudConfigImpl(configList: MutableList<CloudConfigBean>, callBack: ConfigCallback) {
        DebugUtil.i(TAG, " getCloudConfigImpl ", true)
        // 同步停止
        if (CloudSyncAgent.getInstance().isManualStop()) {
            callBack.onError(
                CloudSyncResult.createResult(
                    CloudSyncAgent.getInstance().getStopSubErrorCode(),
                    CloudSyncResult.ERROR_FROM_METADATA_RECOVERY))
            return
        }
        // 检测开关、网络、快稳省问题
        val checkSwitchResult = RecordSyncChecker.checkCloudSwitchState(BaseApplication.getAppContext())
        if (!checkSwitchResult.success()) {
            callBack.onError(CloudSyncResult.createResult(checkSwitchResult.resultCode, CloudSyncResult.ERROR_FROM_METADATA_RECOVERY))
            return
        }
        // module：传入开发者平台申请的用于获取公共配置的container
        // zone：传入开发者平台申请的用于获取公共配置的zone
        // requestSource：传入CloudRecoveryRequestSource.OTHERS
        CloudSyncManager.getInstance().startRecoveryMetaData(
            SyncDataConstants.MODULE_RECORDER, SyncDataConstants.ZONE_CONFIG,
            CloudRecoveryRequestSource.OTHERS, CloudDataType.PUBLIC, SyncDataConstants.RECORD_TYPE_VERSION, object : ICloudRecoveryMetaData {
                override fun onSuccess(
                    cloudDataType: CloudDataType,
                    data: MutableList<CloudMetaDataRecord>?,
                    hasMoreData: Boolean,
                    totalCount: Long,
                    remainCount: Long,
                ) {
                    DebugUtil.i(TAG, "hasMoreData:$hasMoreData, totalCount:$totalCount,remainCount:$remainCount, data:$data ", true)
                    data?.forEach {
                        it.toCloudConfigBean(mGson)?.also {
                            configList.add(it)
                        }
                    }
                    if (hasMoreData) {
                        getCloudConfigImpl(configList, callBack)
                    } else {
                        callBack.onSuccess(configList)
                    }
                }

                override fun onError(cloudDataType: CloudDataType, error: CloudKitError) {
                    DebugUtil.e(TAG, "onError $error")
                    callBack.onError(CloudSyncResult.createFileError(error, false))
                }
            })
    }

    private fun CloudMetaDataRecord.toCloudConfigBean(gson: Gson): CloudConfigBean? {
        try {
            return gson.fromJson(fields, CloudConfigBean::class.java)?.apply {
                sysRecordType = getSysRecordType()
                DebugUtil.e(TAG, "value   $value")
                if (this.value.isNullOrBlank().not()) {
                    this.dataBean = gson.fromJson(this.value, CloudConfigBean.DataBean::class.java)
                }
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "toCloudConfigBean error $e, metaData is $this")
        }
        return null
    }

    interface ConfigCallback {
        fun onSuccess(data: List<CloudConfigBean>)
        fun onError(error: CloudSyncResult)
    }
}