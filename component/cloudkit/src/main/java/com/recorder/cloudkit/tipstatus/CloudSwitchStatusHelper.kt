package com.recorder.cloudkit.tipstatus

import android.content.Context
import android.os.Build
import android.os.Handler
import com.heytap.cloud.sdk.base.CloudStatusHelper
import com.heytap.cloud.sdk.base.CloudStatusHelper.CloudStatusObserver
import com.heytap.cloudkit.libsync.cloudswitch.bean.CloudSyncSwitchObserver
import com.heytap.cloudkit.libsync.cloudswitch.bean.SwitchState
import com.heytap.cloudkit.libsync.cloudswitch.compat.CloudKitSwitchCompatUtil
import com.heytap.cloudkit.libsync.ext.CloudSyncManager
import com.soundrecorder.base.utils.DebugUtil
import android.os.HandlerThread
import com.recorder.cloudkit.sync.CloudSynStateHelper

/**
 * 监听云同步开关变化
 */
class CloudSwitchStatusHelper constructor(private var context: Context) {

    companion object {
        private const val TAG = "CloudSwitchStatusHelper"
    }

    var mOldSwitchObserver: CloudStatusObserver? = null
    var mKitSwitchObserver: CloudSyncSwitchObserver? = null
    private var mHandlerThread: HandlerThread? = null
    private var mHandler: Handler? = null
    private var mSwitchStateListeners: MutableList<CloudSwitchStateChangeListener> = mutableListOf()

    fun addCloudSwitchChangeListener(listener: CloudSwitchStateChangeListener) {
        if (!mSwitchStateListeners.contains(listener)) {
            mSwitchStateListeners.add(listener)
        }
    }

    fun removeCloudSwitchChangeListener(listener: CloudSwitchStateChangeListener) {
        mSwitchStateListeners.remove(listener)
    }

    /**
     * 注册云同步开关变化
     */
    fun registerSwitchObserver() {
        if (mHandlerThread != null) {
            DebugUtil.e(TAG, "mHandlerThread is already init")
            return
        }
        mHandlerThread = HandlerThread("CloudSwitch")
        mHandlerThread?.start()
        mHandler = Handler(mHandlerThread!!.looper).apply {
            post {
                registerOldSwitchObserver(this)
                registerCloudKitSwitchObserver(this)
            }
        }
    }

    /**
     * 反注册云同步开关变化
     */
    fun unregisterSwitchObserver() {
        mHandler?.post {
            if (mOldSwitchObserver != null) {
                CloudStatusHelper.unRegisterCloudOldSwitchChange(context, mOldSwitchObserver)
                mOldSwitchObserver = null
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                if (mKitSwitchObserver != null) {
                    CloudSyncManager.getInstance().unRegisterSyncSwitchObserver(context, mKitSwitchObserver)
                    mKitSwitchObserver = null
                }
            }
            mHandlerThread?.quitSafely()
            mHandlerThread = null
            mHandler = null
            mSwitchStateListeners.clear()
        }
    }

    /**
     * 兼容老版本云服务APP开关变化
     */
    private fun registerOldSwitchObserver(handler: Handler) {
        if (CloudKitSwitchCompatUtil.isSupportSwitch(context).isSuccess) {
            DebugUtil.i(TAG, "current cloud app support appSetting")
            return
        }
        DebugUtil.i(TAG, " register old cloud switch observer")
        mOldSwitchObserver = object : CloudStatusObserver(handler) {
            override fun onChange(path: String) {
                val state = CloudSynStateHelper.getSwitchStateOldVersion()
                DebugUtil.i(TAG, "onChange  $path  state is $state")
                notifySwitchStateChanged(SwitchState.getSwitchState(state))
            }
        }
        CloudStatusHelper.registerCloudOldSwitchChange(context,
            arrayListOf(CloudStatusHelper.NotifyPath.RECORD_SYNC), mOldSwitchObserver)
    }

    /**
     * 监听云同步APP打开/关闭录音开关
     */
    private fun registerCloudKitSwitchObserver(handler: Handler) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            DebugUtil.i(TAG, " register cloudKit switch observer")
            mKitSwitchObserver = object : CloudSyncSwitchObserver(handler) {
                /**
                 * @param isChangeBySdk false 目前自云服务非sdk接口修改的；true：调用sdk修改开关触发修改的；
                 */
                override fun onSyncSwitchChange(switchState: SwitchState, isChangeBySdk: Boolean) {
                    DebugUtil.i(TAG, "onChange switchState is $switchState ,isChangeBySdk  $isChangeBySdk")
                    notifySwitchStateChanged(switchState)
                }
            }
            CloudSyncManager.getInstance().registerSyncSwitchObserver(context, mKitSwitchObserver)
        }
    }

    private fun notifySwitchStateChanged(switchState: SwitchState) {
        mSwitchStateListeners.forEach {
            it.onCloudSwitchChanged(switchState)
        }
    }

    interface CloudSwitchStateChangeListener {
        fun onCloudSwitchChanged(switchState: SwitchState)
    }
}