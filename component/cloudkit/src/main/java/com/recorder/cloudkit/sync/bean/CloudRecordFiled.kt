package com.recorder.cloudkit.sync.bean

import com.soundrecorder.base.BaseApplication
import com.recorder.cloudkit.utils.PathUtil
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.db.GroupInfoDbUtil
import com.soundrecorder.common.utils.MarkSerializUtil.parseMarkDataBeanListFromString
import java.nio.charset.StandardCharsets

const val VERSION_DEFAULT = 0L

/**
 * 对应元数据 SDK中CloudMetaDataRecord.java 中fileds字段
 * 兼容老版本Sdk云端老数据
 */
class CloudRecordField {
    var duration: Long = 0
    var fileId: String? = null
    var fileMD5: String? = null
    var fileSize: Long = 0
    var markData: String? = null
    var mimeType: String? = null
    var recordTime: Long = 0
    var recordUpdateTime: Long = 0
    var recordType: String? = null

    //相对录音，含文件名称
    var relativePath: String? = null

    // uuid
    var itemId: String? = null

    var isDirectOn: Int = 0
    var directTime: String? = null

    var groupUuid: String? = null
    var callerName: String? = null
    var originalName: String? = null

    fun toRecord(): Record {
        val record = Record()
        record.uuid = itemId
        record.duration = duration
        record.mD5 = fileMD5
        record.fileSize = fileSize
        record.mimeType = mimeType
        record.dateCreated = recordTime
        record.dateModied = recordUpdateTime
        record.recordType = recordType?.toInt() ?: 0
        record.relativePath = PathUtil.getDownloadRelativePath(relativePath)
        record.displayName = PathUtil.getNameFromPath(relativePath)
        record.data = PathUtil.getDownloadDataPath(BaseApplication.getAppContext(), relativePath)
        record.fileId = fileId
        if (markData.isNullOrBlank().not()) {
            record.markData = markData?.toByteArray(StandardCharsets.UTF_8)
            record.markDataBeanList = parseMarkDataBeanListFromString(markData)
        }
        if (directTime != null) {
            record.directOn = isDirectOn == 1
            record.directTime = directTime
        }
        record.groupUuid = groupUuid
        record.groupId = GroupInfoDbUtil.getGroupIdByGroupUUID(BaseApplication.getAppContext(), groupUuid)
        record.originalName = originalName
        return record
    }

    companion object {
        @JvmStatic
        fun toMetaData(record: Record): CloudRecordField = CloudRecordField().apply {
            duration = record.duration
            fileId = record.fileId
            fileMD5 = record.mD5
            fileSize = record.fileSize
            if ((record.markData != null) && (record.markData.isNotEmpty())) {
                markData = String(record.markData, StandardCharsets.UTF_8)
            }
            mimeType = record.mimeType
            recordTime = record.dateCreated
            recordUpdateTime = record.dateModied
            recordType = record.recordType.toString()
            relativePath = PathUtil.getUploadConcatedPath(record)
            itemId = record.uuid
            if (record.directTime != null) {
                isDirectOn = if (record.directOn) 1 else 0
                directTime = record.directTime
            }
            groupUuid = record.groupUuid
            originalName = record.originalName
        }
    }
}