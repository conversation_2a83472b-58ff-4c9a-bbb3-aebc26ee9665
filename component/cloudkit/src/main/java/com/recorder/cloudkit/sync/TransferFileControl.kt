package com.recorder.cloudkit.sync

import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.heytap.cloudkit.libsync.ext.CloudSyncManager
import com.heytap.cloudkit.libsync.io.CloudIOFileListener
import com.heytap.cloudkit.libsync.io.CloudIOLogger
import com.heytap.cloudkit.libsync.service.CloudDataType
import com.heytap.cloudkit.libsync.service.CloudIOFile
import com.soundrecorder.base.utils.DebugUtil
import com.recorder.cloudkit.sync.bean.RecordTransferFile
import java.util.concurrent.CountDownLatch

object TransferFileControl {
    private const val TAG = "TransferFileControl"


    /**
     * 上传文件
     */
    fun uploadFileList(transferFileList: List<RecordTransferFile>, transferFilesCallBack: TransferFilesCallBack) {
        if (transferFileList.isNullOrEmpty()) {
            DebugUtil.i(TAG, "uploadFileList is empty ", true)
            transferFilesCallBack.batchResult(emptyList(), emptyList())
            return
        }
        val latch = CountDownLatch(transferFileList.size)
        val successFileList: MutableList<RecordTransferFile> = ArrayList()
        val errorFileList: MutableList<RecordTransferFile> = ArrayList()

        transferFilesCallBack.onTransferStart()

        for (transferFile in transferFileList) {
            CloudSyncManager.getInstance()
                .transferFile(transferFile.cloudIOFile, CloudDataType.PRIVATE, object : CloudIOFileListener {

                    override fun onProgress(file: CloudIOFile, cloudDateType: CloudDataType, progress: Double) {
                        DebugUtil.i(TAG, "uploaFile - onProgress $progress")
                    }

                    override fun onFinish(file: CloudIOFile, cloudDateType: CloudDataType, cloudKitError: CloudKitError) {
                        DebugUtil.i(TAG, "uploaFile-onFinish $latch result: $cloudKitError  file:\n" + CloudIOLogger.getPrintLog(file), true)

                        transferFile.cloudIOFile = file
                        transferFile.uploadResult = cloudKitError
                        // 回调单个上传结果
                        transferFilesCallBack.onIOResult(transferFile, cloudKitError)

                        callBatchCallBack(transferFile, cloudKitError)
                    }

                    fun callBatchCallBack(file: RecordTransferFile, cloudKitError: CloudKitError) {
                        synchronized(latch) {
                            if (cloudKitError.isSuccess) {
                                successFileList.add(file)
                            } else {
                                errorFileList.add(file)
                            }
                            latch.countDown()
                            if (latch.count == 0L) {
                                DebugUtil.i(TAG,
                                    "uploaFile-successFileList:${successFileList.size}, errorFileList:$errorFileList", true)
                                transferFilesCallBack.batchResult(successFileList, errorFileList)
                            }
                        }
                    }
                })
        }
    }

    /**
     * 下载文件
     */
    fun downLoadFileList(transferFileList: List<RecordTransferFile>, transferFilesCallBack: TransferFilesCallBack) {
        if (transferFileList.isNullOrEmpty()) {
            DebugUtil.i(TAG, "downloadFileList is empty ", true)
            transferFilesCallBack.batchResult(emptyList(), emptyList())
            return
        }

        val latch = CountDownLatch(transferFileList.size)
        val successFileList: MutableList<RecordTransferFile> = ArrayList()
        val errorFileList: MutableList<RecordTransferFile> = ArrayList()

        transferFilesCallBack.onTransferStart()

        for (transferFile in transferFileList) {
            CloudSyncManager.getInstance().transferFile(transferFile.cloudIOFile, CloudDataType.PRIVATE, object : CloudIOFileListener {

                override fun onProgress(file: CloudIOFile, cloudDateType: CloudDataType, progress: Double) {
                    DebugUtil.i(TAG, "downloadFile - onProgress $progress")
                }

                override fun onFinish(file: CloudIOFile, cloudDateType: CloudDataType, cloudKitError: CloudKitError) {
                    DebugUtil.i(TAG, "downloadFile-onFinish $latch result: $cloudKitError  file:\n" + CloudIOLogger.getPrintLog(file), true)
                    transferFile.cloudIOFile = file
                    transferFile.uploadResult = cloudKitError
                    // 单个下载结果
                    transferFilesCallBack.onIOResult(transferFile, cloudKitError)

                    callBatchCallBack(transferFile, cloudKitError)
                }

                fun callBatchCallBack(file: RecordTransferFile, cloudKitError: CloudKitError) {
                    synchronized(latch) {
                        if (cloudKitError.isSuccess) {
                            successFileList.add(file)
                        } else {
                            errorFileList.add(file)
                        }
                        latch.countDown()
                        if (latch.count == 0L) {
                            DebugUtil.i(TAG, "downloadFile-successFileList:${successFileList.size}, errorFileList:$errorFileList", true)
                            transferFilesCallBack.batchResult(successFileList, errorFileList)
                        }
                    }
                }
            })
        }
    }


    /**
     * 多个文件上传/下载回调
     */
    interface TransferFilesCallBack {
        /**
         * 批次上传完成，所有结果
         */
        fun batchResult(successFileList: List<RecordTransferFile>?, errorFileList: List<RecordTransferFile>?)

        // 单个文件上传结果
        fun onIOResult(file: RecordTransferFile, cloudKitError: CloudKitError)

        fun onTransferStart()
    }
}