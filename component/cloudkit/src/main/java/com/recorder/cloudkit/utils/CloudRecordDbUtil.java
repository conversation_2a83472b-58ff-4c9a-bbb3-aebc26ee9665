/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: CloudRecordDbUtil
 * Description:
 * Version: 1.0
 * Date: 2022/10/25
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2022/10/25 1.0 create
 */

package com.recorder.cloudkit.utils;

import android.content.Context;

import com.heytap.cloudkit.libsync.netrequest.metadata.CloudMetaDataRecord;
import com.heytap.cloudkit.libsync.service.CloudIOFile;
import com.recorder.cloudkit.sync.SyncDataConstants;
import com.recorder.cloudkit.sync.backup.FileUpLoadUtil;
import com.recorder.cloudkit.sync.bean.RecordTransferFile;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.common.constant.RecordConstant;
import com.soundrecorder.common.databean.Record;
import com.soundrecorder.common.db.RecorderDBUtil;

import java.util.ArrayList;
import java.util.List;

public class CloudRecordDbUtil {
    public static final String TAG = "CloudRecordDbUtil";
    /*备份每批次支持文件最大大小、总数量*/
    private static final Long MAX_FILE_SIZE = 50 * 1024 * 1024L;
    private static final int MAX_FILE_COUNT = 100;

    /**
     * 查询数据库已下载元数据，未下载文件的记录(有global_id、fileId，无data)
     */
    public static List<RecordTransferFile> getDownloadFileList(Context context) {
        List downloadFileList = new ArrayList<RecordTransferFile>();
        long totalSize = 0L;
        int fileCount = 0;
        int priority = -1;
        try {
            List<Record> recordList = RecorderDBUtil.getInstance(context).getDataForBatchDownloadFile();
            List<String> fileIdList = new ArrayList<>();
            DebugUtil.d(TAG, "getDataForBatchDownloadFile size:" + recordList.size(), true);
            if (!recordList.isEmpty()) {
                for (Record record : recordList) {
                    if (fileIdList.contains(record.getFileId())) { // 对文件ID去重
                        continue;
                    }
                    long size = record.getFileSize();
                    boolean canStart = record.canStartSync();
                    boolean needDownload = !record.fileExistAndCheckSize() && record.getSyncPrivateStatus() != RecordConstant.RECORD_PRIVETE_ENCRYPT;
                    if (size <= 0 || !canStart || !needDownload) {
                        DebugUtil.v(TAG, "getDownloadFileList-size:" + size
                                + ",canStart:" + canStart + ",needDownload:" + needDownload, true);
                        if (!needDownload) {
                            //file exist , no need to download files and mark this record to du
                            RecorderDBUtil.getInstance(context).updateDownloadStateByUUid(record.getUuid(),
                                    RecordConstant.SYNC_STATUS_RECOVERY_FILE_SUC);
                        }
                        continue;
                    }
                    String fileId = record.getFileId();
                    String filePath = record.getData();
                    String fileMD5 = record.getMD5();
                    int prority = FileUpLoadUtil.getPriorityBySize(size);
                    if (filePath == null) {
                        DebugUtil.w(TAG, "getDataForBatchDownloadFile-filePath is null.");
                    }
                    CloudIOFile cloudIOFile = CloudIOFile.createDownloadFile(record.getGlobalId(),
                            SyncDataConstants.MODULE_RECORDER, SyncDataConstants.ZONE_RECORDER, fileMD5, fileId, filePath);
                    if (priority == -1) {
                        priority = prority;
                    }
                    totalSize += size;
                    fileCount++;
                    if ((totalSize < MAX_FILE_SIZE) && (fileCount < MAX_FILE_COUNT) && (prority == priority)) {
                        downloadFileList.add(new RecordTransferFile(record, cloudIOFile));
                        fileIdList.add(fileId);
                    } else if (fileCount <= 1) {
                        downloadFileList.add(new RecordTransferFile(record, cloudIOFile));
                        fileIdList.add(fileId);
                    } else {
                        return downloadFileList;
                    }
                }
                return downloadFileList;
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "getUploadFileList-e:", e);
        }
        return downloadFileList;
    }

    public static List<RecordTransferFile> getDownloadFileList(Context context, List<CloudMetaDataRecord> cloudMetaDataList) {
        DebugUtil.d(TAG, "getDownloadFileList- by globalIdlIst", true);
        List downloadFileList = new ArrayList<RecordTransferFile>();
        try {
            if (cloudMetaDataList == null || cloudMetaDataList.isEmpty()) {
                DebugUtil.e(TAG, "getDownloadFileList cloudMetaDataList is null or empty", true);
                return downloadFileList;
            }
            List<String> globalIdList = new ArrayList<>();
            for (CloudMetaDataRecord metaDataRecord : cloudMetaDataList) {
                if (metaDataRecord.getSysStatus() == SyncDataConstants.SYS_STATUS_NORMAL) {
                    globalIdList.add(metaDataRecord.getSysRecordId());
                }
            }
            List<Record> recordList = RecorderDBUtil.getInstance(context).getDataForBatchDownloadFile(globalIdList);
            DebugUtil.d(TAG, "getDataForBatchDownloadFile cloudMetaDataList size is " + cloudMetaDataList.size()
                    + ",record size:" + recordList.size(), true);
            if (!recordList.isEmpty()) {
                List<String> fileIdList = new ArrayList<>();
                RecordTransferFile transferFile = null;
                for (Record record : recordList) {
                    // 去重
                    if (fileIdList.contains(record.getFileId())) {
                        continue;
                    }
                    long size = record.getFileSize();
                    boolean canStart = record.canStartSync();
                    boolean needDownload =
                            !record.fileExistAndCheckSize() && record.getSyncPrivateStatus() != RecordConstant.RECORD_PRIVETE_ENCRYPT;
                    if (size <= 0 || !canStart || !needDownload) {
                        DebugUtil.v(TAG,
                                "getDataForBatchDownloadFile-size:" + size
                                        + ", canStart:" + canStart + ", needDownload:" + needDownload, true);
                        if (!needDownload) {
                            //file exist , no need to download files and mark this record to du
                            RecorderDBUtil.getInstance(context)
                                    .updateDownloadStateByUUid(record.getUuid(), RecordConstant.SYNC_STATUS_RECOVERY_FILE_SUC);
                        }
                        continue;
                    }
                    String fileId = record.getFileId();
                    String filePath = record.getData();
                    String fileMD5 = record.getMD5();
                    if (filePath == null) {
                        DebugUtil.w(TAG, "getDataForBatchDownloadFile-filePath is null.");
                    }
                    CloudIOFile cloudIOFile = CloudIOFile.createDownloadFile(record.getGlobalId(), SyncDataConstants.MODULE_RECORDER,
                            SyncDataConstants.ZONE_RECORDER, fileMD5, fileId, filePath);
                    transferFile = new RecordTransferFile(record, cloudIOFile);
                    downloadFileList.add(transferFile);
                    fileIdList.add(record.getFileId());
                }
                return downloadFileList;
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "getUploadFileList-e", e);
        }
        return downloadFileList;
    }
}
