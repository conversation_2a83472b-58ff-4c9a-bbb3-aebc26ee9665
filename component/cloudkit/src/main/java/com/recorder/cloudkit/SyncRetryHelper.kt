package com.recorder.cloudkit

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import com.soundrecorder.base.utils.DebugUtil
import java.text.SimpleDateFormat
import java.util.*


object SyncRetryHelper {
    private const val TAG = "SyncRetryHelper"

    @JvmStatic
    @Synchronized
    fun startRetryDelayAlarm(context: Context, time: Long, arg: Int) {
        getAlarmPendingIntent(context, arg)?.let {
            val am = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            if (am != null) {
                val sdkVersion = Build.VERSION.RELEASE.toUpperCase()
                if (sdkVersion.compareTo("6.0") >= 0) {
                    am.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, time, it)
                } else {
                    am.setExact(AlarmManager.RTC_WAKEUP, time, it)
                }
            } else {
                DebugUtil.e(TAG, "startRetryDelayAlarm, AlarmManager is null.")
            }
        }
    }

    @JvmStatic
    @Synchronized
    fun clearClearFailedCountAlarm(context: Context?) {
        context?.let {
            DebugUtil.i(TAG, "clearClearFailedCountAlarm", true)
            getAlarmPendingIntentForClearTask(it)?.apply {
                val manager = it.getSystemService(Context.ALARM_SERVICE) as AlarmManager
                this.cancel()
                manager.cancel(this)
            }
        }
    }


    @JvmStatic
    @Synchronized
    fun startClearFailCountDelayAlarm(context: Context) {
        getAlarmPendingIntentForClearTask(context)?.let {
            val am = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            val time: Long = getTimeForClearTask()
            val df = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
            if (am != null) {
                val sdkVersion = Build.VERSION.RELEASE.toUpperCase()
                if (sdkVersion.compareTo("6.0") >= 0) {
                    am.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, time, it)
                } else {
                    am.setExact(AlarmManager.RTC_WAKEUP, time, it)
                }
                DebugUtil.i(TAG,
                    "startClearFailCountDelayAlarm at time: " + df.format(Date(time)) + ", pIntent: " + it + ", sdkVersion: " + sdkVersion, true)
            } else {
                DebugUtil.e(TAG, "startClearFailCountDelayAlarm, AlarmManager is null.")
            }
        }
    }


    @JvmStatic
    @Synchronized
    fun clearSyncAlarm(context: Context?, arg: Int) {
        context?.let {
            DebugUtil.i(TAG, "clearSyncAlarm", true)
            val pIntent = getAlarmPendingIntent(it, arg)
            if (pIntent != null) {
                val manager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
                pIntent.cancel()
                manager.cancel(pIntent)
            }
        }
    }

    private fun getAlarmPendingIntent(context: Context, retryType: Int): PendingIntent? {
        val intent = Intent(SyncRetryReceiver.RETRY_SYNC_ACTION)
        intent.putExtra(SyncRetryReceiver.EXTRA_RETRY_TYPE, retryType)
        intent.setPackage(context.packageName)
        return PendingIntent.getBroadcast(context, 0, intent, PendingIntent.FLAG_IMMUTABLE)
    }

    private fun getAlarmPendingIntentForClearTask(context: Context): PendingIntent? {
        val intent = Intent(SyncRetryReceiver.CLEAR_FAILED_ACTION)
        intent.setPackage(context.packageName)
        return PendingIntent.getBroadcast(context, 1, intent, PendingIntent.FLAG_IMMUTABLE)
    }

    private fun getTimeForClearTask(): Long {
        val mCalendar = Calendar.getInstance()
        mCalendar.timeInMillis = System.currentTimeMillis()
        val systemTime = System.currentTimeMillis()
        //set currentTime to make the time of calendar to sync to now
        mCalendar.timeInMillis = System.currentTimeMillis()
        //set the time zone
        mCalendar.timeZone = TimeZone.getTimeZone("GMT+8")
        //set the time 02:00 for the day
        mCalendar[Calendar.HOUR_OF_DAY] = 2
        mCalendar[Calendar.MINUTE] = 0
        mCalendar[Calendar.SECOND] = 0
        mCalendar[Calendar.MILLISECOND] = 0
        //get select time for the calendar
        val selectTime = mCalendar.timeInMillis
        // if now is big than 02:00, add a day
        if (systemTime > selectTime) {
            mCalendar.add(Calendar.DAY_OF_MONTH, 1)
        }
        return mCalendar.timeInMillis
    }
}