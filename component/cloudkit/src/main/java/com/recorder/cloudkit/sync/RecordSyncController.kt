package com.recorder.cloudkit.sync

import android.content.Context
import android.os.Build
import com.heytap.cloudkit.libsync.cloudswitch.bean.CloudSyncState
import com.heytap.cloudkit.libsync.ext.CloudSyncManager
import com.heytap.cloudkit.libsync.metadata.helper.CloudBackupRequestSource
import com.heytap.cloudkit.libsync.metadata.helper.CloudRecoveryRequestSource
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudBackupResponseError
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudBackupResponseRecord
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudMetaDataRecord
import com.recorder.cloudkit.sync.bean.CloudSyncResult
import com.recorder.cloudkit.sync.bean.RecordTransferFile
import com.recorder.cloudkit.sync.bean.constant.RecordSyncState
import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode
import com.recorder.cloudkit.sync.config.CloudConfigBean
import com.recorder.cloudkit.sync.config.CloudConfigFetcher
import com.recorder.cloudkit.sync.config.CloudConfigUtil
import com.recorder.cloudkit.sync.listener.IBackUpListener
import com.recorder.cloudkit.sync.listener.IRecoveryListener
import com.recorder.cloudkit.tipstatus.TipStatusManager
import com.recorder.cloudkit.utils.CloudKitErrorExt.calFinalErrorCode
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.base.utils.SyncTimeUtils
import com.soundrecorder.common.buryingpoint.CloudStaticsUtil
import com.soundrecorder.common.db.GroupInfoManager
import com.soundrecorder.common.db.RecorderDBUtil
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.utils.Injector

class RecordSyncController(val context: Context) {
    companion object {
        private const val TAG = "RecordSyncController"
        private const val META_DATA_MAX_TRY_COUNT = 1
        private const val BACK_UP_STEP0_1 = "commitUnCommitMetaDataOfGroupInfo"
        private const val BACK_UP_STEP0_2 = "commitChangedMetaDataOfGroupInfo"
        private const val BACK_UP_STEP1 = "commitUnCommitMetaData"
        private const val BACK_UP_STEP2 = "commitChangedMetaData"
        private const val BACK_UP_STEP3 = "commitUnUploadFileAndMetaData"
        private const val CHECK_ISSUE_MIME_TYPE_KEY = "check_issue_mime_type_records"
        private const val CHECK_ISSUE_MIME_TYPE_MAX_COUNT = 10
    }

    private val mSyncManager: CloudSyncAgent = CloudSyncAgent.getInstance()

    // 当前恢复/备份 同步流程状态
    @Volatile
    var mSyncBackUpState: Int = RecordSyncState.BACKUP_COMPLETED
    @Volatile
    var mSyncRecoveryState: Int = RecordSyncState.RECOVERY_COMPLETED

    // 记录本次流程开端 以及中间 上传下载文件错误码
    private var isRecoveryEnter = false

    // 目前仅在上传下载文件过程中，忽略了的错误码(可重试错误码)
    private var canContinueProcessCode = 0
    /*记录媒体库全量对比被取消，下次进入继续执行*/
    private var needContinueMediaCompareNext = false

    private val cloudKitApi by lazy {
        Injector.injectFactory<CloudKitInterface>()
    }

    fun setManualStop(stop: Boolean, subErrorCode: Int? = null) {
        DebugUtil.i(TAG, "setManualStop $stop", true)
        mSyncManager.setSyncStop(stop, subErrorCode)
        checkNeedStopMediaCompare(stop)
    }

    private fun checkNeedStopMediaCompare(stop: Boolean) {
        // 只停止同步流程中触发的媒体库全量对比，避免停掉正常逻辑的媒体库全量对比
        if (mSyncRecoveryState == RecordSyncState.RECOVERY_SCAN_MEDIA) {
            DebugUtil.i(TAG, "checkNeedStopMediaCompare")
            cloudKitApi?.doStopMediaCompare(stop)
            if (stop) {
                // 媒体库对比阶段被停止，标记为true
                needContinueMediaCompareNext = true
            }
        }
    }

    /**
     * @param requestSource
     * DATA_CHANGE: 录音使用过程中，新增变更的文件
     * MANUAL：冷启动or其他场景恢复流程完成后走的备份
     * OTHERS：上传文件or元数据失败后的重试
     */
    fun doBackUp(requestSource: CloudBackupRequestSource) {
        doBackUpInternal(null, false, requestSource)
    }

    /**
     * @param requestSource
     * PUSH：收到云端推送，云端内容有变更
     * MANUAL：用户开启云同步开关，后面处理冲突场景需要
     * START_APP：页面冷启动
     * NEED_FETCH：备份过程中，服务端返回错误码，有新数据需要拉
     * OTHERS：其他，如下载失败文件。重试逻辑
     */
    fun doRecovery(scanMedia: Boolean, requestSource: CloudRecoveryRequestSource) {
        doRecoveryInternal(scanMedia, requestSource)
    }

    fun isSyncRunning(): Boolean {
        return (mSyncBackUpState != RecordSyncState.BACKUP_COMPLETED) || (mSyncRecoveryState != RecordSyncState.RECOVERY_COMPLETED)
    }

    private fun doRecoveryInternal(scanMedia: Boolean = false, requestSource: CloudRecoveryRequestSource) {
        DebugUtil.i(TAG, "doRecoveryInternal scanMedia $scanMedia,requestSource $requestSource", true)
        // 重试逻辑需校验此时没有再执行-元数据的备份和恢复是串行的，不能同时进行
        if (!checkCanDoSync()) {
            CloudStaticsUtil.addCloudLog(TAG, "doRecoveryInternal," +
                    "checkCanDoSync fail by recoveryState$mSyncRecoveryState, backUpState$mSyncBackUpState")
            return
        }
        setManualStop(false)
        canContinueProcessCode = 0
        isRecoveryEnter = true
        // 清空failCount
        RecorderDBUtil.getInstance(context).clearFailedCount(SyncTimeUtils.FAILED_1_COUNT)
        GroupInfoManager.getInstance(context).queryAllCustomGroupInfoList()

        // manual 和 push 不会存在此种脏数据
        if ((requestSource == CloudRecoveryRequestSource.OTHERS) || (requestSource == CloudRecoveryRequestSource.START_APP)) {
            // 清空同步过程中，杀死进程，状态未变化的数据
            RecorderDBUtil.getInstance(context).resetSyncLockedState()
        }

        doRecoveryImpl(null, scanMedia, requestSource)
    }

    /**
     * @param lastSyncState
     * @param scanMedia 是否需要媒体库全量对比，ture：执行媒体库全量对比；false：不触发媒体库全量对比
     */
    private fun doRecoveryImpl(lastSyncState: Int?, scanMedia: Boolean = false, requestSource: CloudRecoveryRequestSource) {
        // 是否需要获取公共配置项，目前需要满足 非push&&距离上一次获取超过3天才更新
        val isNeedUpdateConfig = requestSource != CloudRecoveryRequestSource.PUSH && CloudConfigUtil.checkNeedFetchCloudConfig()
        DebugUtil.i(TAG, "doRecoveryImpl scanMedia:$scanMedia|$needContinueMediaCompareNext, isNeedUpdateConfig:$isNeedUpdateConfig", true)
        CloudStaticsUtil.addCloudLog(
            TAG,
            "doRecoveryImpl,requestSource ${requestSource.type} scanMedia:$scanMedia, isNeedUpdateConfig $isNeedUpdateConfig")
        // 更新副标题：查询中
        var syncState = lastSyncState
        if (lastSyncState == null) {
            if (scanMedia || isNeedUpdateConfig || needContinueMediaCompareNext) {
                changeUiQueryOrSyncingState(SyncErrorCode.UI_STATE_QUERYING)
                syncState = SyncErrorCode.UI_STATE_QUERYING
            }
        }
        // 媒体库全量对比
        if (scanMedia || needContinueMediaCompareNext) {
            mSyncRecoveryState = RecordSyncState.RECOVERY_SCAN_MEDIA
            cloudKitApi?.doMediaCompare(false)
            if (!mSyncManager.isManualStop()) {
                // 非停止下执行完媒体库全量对比，标记为false
                needContinueMediaCompareNext = false
            }
        }
        // 获取公共配置项
        if (isNeedUpdateConfig) {
            mSyncRecoveryState = RecordSyncState.RECOVERY_GET_CONFIG
            CloudConfigFetcher.getCloudConfig(object : CloudConfigFetcher.ConfigCallback {
                override fun onSuccess(data: List<CloudConfigBean>) {
                    CloudConfigUtil.saveCloudConfigToCache(BaseApplication.getAppContext(), data)
                    doRecoveryFirst(lastSyncState = syncState, requestSource = requestSource)
                }

                override fun onError(error: CloudSyncResult) {
                    DebugUtil.e(TAG, "getCloudConfig error $error ")
                    notifySyncResult(error)
                    mSyncRecoveryState = RecordSyncState.RECOVERY_COMPLETED
                    CloudStaticsUtil.addCloudLog(TAG, "doRecoveryImpl,onError: ${error.logMessage()}")
                }
            })
        } else {
            doRecoveryFirst(lastSyncState = syncState, requestSource = requestSource)
        }
    }

    private fun doBackUpInternal(lastSyncState: Int?, fromRecovery: Boolean, requestSource: CloudBackupRequestSource) {
        // 重试逻辑需校验此时没有再执行-元数据的备份和恢复是串行的，不能同时进行
        if (!checkCanDoSync()) {
            CloudStaticsUtil.addCloudLog(TAG, "doBackUpInternal," +
                    "checkCanDoSync fail by recoveryState$mSyncRecoveryState, backUpState$mSyncBackUpState")
            return
        }
        // 记录单次同步入口为recovery or not
        isRecoveryEnter = fromRecovery

        if (!fromRecovery) {
            // 重置开始标志位
            setManualStop(false)
            canContinueProcessCode = 0
            // 清空failCount
            RecorderDBUtil.getInstance(context).clearFailedCount(SyncTimeUtils.FAILED_1_COUNT)
        }
        CloudStaticsUtil.addCloudLog(TAG, "doBackUpInternal,fromRecovery=$fromRecovery,requestSource=${requestSource.type}," +
                "localCount= ${RecorderDBUtil.getInstance(context).needBackUpCount}")
        doBackUpGroupInfoStep01(lastSyncState, requestSource = requestSource)
    }

    /**
     * 调整先下载元数据再下载上一次失败的文件，避免云端云数据删除变更(本地未下载)，无效下载
     * step1.下载元数据->下载文件
     * step2.下载文件
     * @param lastSyncState 当前已经更新的同步状态，主要针对查询中和同步中两种状态，避免重复调用
     * @param requestSource
     */
    private fun doRecoverySecond(lastSyncState: Int?, requestSource: CloudRecoveryRequestSource) {
        CloudStaticsUtil.addCloudLog(TAG, "doRecoverySecond, lastSyncState=$lastSyncState")
        // 查询默认状态，若是NEED_FETCH,则是由备份而来，不会存在query的state
        var syncState: Int? = lastSyncState
        //step1 优先下载未下载文件
        mSyncManager.recoveryUnDownloadData(requestSource, object : IRecoveryListener {
            // 开始下载文件
            override fun onStartDownloadFile() {
                mSyncRecoveryState = RecordSyncState.RECOVERY_FILE_START
                // 若存在补刀逻辑，则无【查询中】状态，直接变更为【同步中】状态
                if (syncState != SyncErrorCode.UI_STATE_SYNCING) {
                    syncState = SyncErrorCode.UI_STATE_SYNCING
                    changeUiQueryOrSyncingState(syncState!!)
                }
            }

            // 每批次文件下载结果（这里可能会回调好几次）
            override fun onDownloadFileFinish(successFileList: List<RecordTransferFile>?, errorFileList: List<RecordTransferFile>?) {
                mSyncRecoveryState = if (errorFileList.isNullOrEmpty()) RecordSyncState.RECOVERY_FILE_SUCCESS else RecordSyncState.RECOVERY_FILE_FAIL
                canContinueProcessCode = errorFileList?.getOrNull(0)?.uploadResult?.calFinalErrorCode() ?: canContinueProcessCode
            }

            /**
             * 流程结束
             * @param cloudKitError
             * success: 没有出现不可恢复的错误 or 执行0次（没有需要下载的db记录）
             * fail: 再某次中 出现不可恢复的错误
             */
            override fun onFinish(cloudKitError: CloudSyncResult) {
                if (cloudKitError.isSuccess()) {
                    DebugUtil.i(TAG, "doRecoverySecond finish-success: $requestSource", true)
                    // 非推送过来的，都走一遍备份流程
                    if ((!needBackUpWhenPush()) && (requestSource == CloudRecoveryRequestSource.PUSH)) {
                        notifySyncResult(cloudKitError)
                        // 标志本次恢复完成，需在notifySyncResult后复位，否则可能会修改 isRecoveryEnter值
                        mSyncRecoveryState = RecordSyncState.RECOVERY_COMPLETED
                    } else {
                        // 若要执行备份流程，需再备份之前标志本次恢复完成
                        mSyncRecoveryState = RecordSyncState.RECOVERY_COMPLETED
                        doBackUpInternal(syncState, true, CloudBackupRequestSource.MANUAL)
                    }
                } else {
                    // 同步流程结束
                    notifySyncResult(cloudKitError)
                    // 无下一步操作，需在notifySyncResult后复位，否则可能影响 isRecoveryEnter 准确性
                    mSyncRecoveryState = RecordSyncState.RECOVERY_COMPLETED
                }
            }
        })
    }

    /**
     * 常规正常恢复流程
     * 下载元数据-下载文件
     * @param lastSyncState 上一步同步状态，对应 SyncErrorCode.querying or syncing
     * @param tryCount >0 代表是代码内部重试逻辑，state不会发生变更
     * @param requestSource
     */
    private fun doRecoveryFirst(lastSyncState: Int?, tryCount: Int = 0, requestSource: CloudRecoveryRequestSource) {
        CloudStaticsUtil.addCloudLog(TAG, "doRecoveryFirst,lastSyncState=$lastSyncState, tryCount=$tryCount,requestSource=${requestSource.type}")
            var syncState: Int? = lastSyncState
        mSyncManager.recoveryCloudData(requestSource, object : IRecoveryListener {
            // 开始下载元数据
            override fun onStartRecovery() {
                mSyncRecoveryState = RecordSyncState.RECOVERY_META_DATA_START
                // 第一页元数据查询到之前UI状态为 【查询中-UI_STATE_QUERYING】
                // 排除 无补刀操作、非重试
                if ((tryCount == 0) && (syncState == null)) {
                    syncState = SyncErrorCode.UI_STATE_QUERYING
                    changeUiQueryOrSyncingState(syncState!!)
                }
            }

            // 每次元数据下载结果
            override fun onDownloadMetaDataFinish(hasMoreData: Boolean, data: List<CloudMetaDataRecord>?) {
                mSyncRecoveryState = RecordSyncState.RECOVERY_META_DATA_SUCCESS
                // 第一页元数据下载完成，变更state为同步中 UI_STATE_SYNCING
                if (syncState == SyncErrorCode.UI_STATE_QUERYING) {
                    syncState = SyncErrorCode.UI_STATE_SYNCING.also {
                        changeUiQueryOrSyncingState(it)
                    }
                }
            }

            // 开始下载文件
            override fun onStartDownloadFile() {
                mSyncRecoveryState = RecordSyncState.RECOVERY_FILE_START
            }

            // 文件下载结果
            override fun onDownloadFileFinish(successFileList: List<RecordTransferFile>?, errorFileList: List<RecordTransferFile>?) {
                mSyncRecoveryState = if (errorFileList.isNullOrEmpty()) RecordSyncState.RECOVERY_FILE_SUCCESS else RecordSyncState.RECOVERY_FILE_FAIL
                canContinueProcessCode = errorFileList?.getOrNull(0)?.uploadResult?.calFinalErrorCode() ?: canContinueProcessCode

                //文件下载成功之后，校准一下分组文件数量信息
                if (errorFileList.isNullOrEmpty()) {
                    GroupInfoManager.getInstance(BaseApplication.getAppContext()).verifyGroupCount()
                }
            }

            /**
             * 流程结束
             * @param  cloudKitError
             * 开始前check本地条件失败（网络、登录状态、开关、快稳省等原因）
             * 元数据：元数据下载失败 or 处理失败（无权限）
             * 文件：元数据下载成功且没有需要下载的文件 or 有文件下载失败且不可恢复错误 or 文件下载成功(部分文件下载失败且可重试原因)且无元数据下载
             */
            override fun onFinish(cloudKitError: CloudSyncResult) {
                if (cloudKitError.isSuccess()) {
                    DebugUtil.i(TAG, "recovery finish-success: $requestSource", true)
                    // step2:云端获取元数据
                    doRecoverySecond(lastSyncState = syncState, requestSource = requestSource)
                } else {
                    // check 是否为元数据失败，是否可重试，可以重试就再试一次
                    if (cloudKitError.errorFromMetaData() && cloudKitError.isCanTry(tryCount, META_DATA_MAX_TRY_COUNT)) {
                        mSyncRecoveryState = RecordSyncState.RECOVERY_META_DATA_FAIL
                        // 元数据下载失败，且可以重试，重试一次
                        delay(cloudKitError.cloudKitError?.delayRetryTime ?: 0)
                        doRecoveryFirst(lastSyncState = syncState, tryCount = (tryCount + 1), requestSource = requestSource)
                        return
                    }
                    // 标记同步未开始
                    if (syncState == SyncErrorCode.UI_STATE_QUERYING) {
                        cloudKitError.inQueryStep = true
                    }
                    notifySyncResult(cloudKitError)
                    // 单次同步流程结束
                    mSyncRecoveryState = RecordSyncState.RECOVERY_COMPLETED
                }
            }
        })
    }

    /**
     * 上传分组表元数据：该方法处理的是从未上传过的数据
     */
    private fun doBackUpGroupInfoStep01(lastSyncState: Int?, tryCount: Int = 0, requestSource: CloudBackupRequestSource) {
        DebugUtil.i(TAG, "doBackUpGroupInfoStep01 $requestSource, tryCount $tryCount", true)
        CloudStaticsUtil.addCloudLog(TAG, "doBackUpGroupInfoStep01,requestSource=$requestSource,lastSyncState=$lastSyncState,tryCount=$tryCount")
        // ui同步中状态,
        if (lastSyncState == null) {
            changeUiQueryOrSyncingState(SyncErrorCode.UI_STATE_SYNCING)
        }
        // 备份：1-上传元数据：还未上传元数据(无globalID)
        mSyncManager.backUpGroupInfoMetaData(requestSource, BACK_UP_STEP0_1, object : IBackUpListener {
            override fun onStartUploadMetaData() {
                mSyncBackUpState = RecordSyncState.BACKUP_META_DATA_START
            }

            override fun onUploadMetaDataFinish(successData: List<CloudBackupResponseRecord>, errorData: List<CloudBackupResponseError>) {
                mSyncBackUpState = if (errorData.isNullOrEmpty()) RecordSyncState.BACKUP_META_DATA_SUCCESS else RecordSyncState.BACKUP_META_DATA_FAIL
                canContinueProcessCode = errorData.getOrNull(0)?.cloudKitError?.calFinalErrorCode() ?: canContinueProcessCode
            }

            override fun onFinish(stepValue: String, cloudKitError: CloudSyncResult) {
                // step0_1 完成 或 失败且不影响
                if (cloudKitError.isSuccess() || !handleBackUpMetaDataErrorContinueOrEnd(stepValue, tryCount, cloudKitError, requestSource)) {
                    doBackUpGroupInfoStep02(lastSyncState, requestSource = requestSource)
                }
            }
        })
    }

    /**
     * 上传分组表元数据：该方法处理的是已经上传过但是本地又有了改变的数据
     */
    private fun doBackUpGroupInfoStep02(lastSyncState: Int?, tryCount: Int = 0, requestSource: CloudBackupRequestSource) {
        DebugUtil.i(TAG, "doBackUpGroupInfoStep02 $requestSource, tryCount $tryCount", true)
        CloudStaticsUtil.addCloudLog(TAG, "doBackUpGroupInfoStep02,requestSource=$requestSource,lastSyncState=$lastSyncState,tryCount=$tryCount")
        // ui同步中状态,
        if (lastSyncState == null) {
            changeUiQueryOrSyncingState(SyncErrorCode.UI_STATE_SYNCING)
        }
        // 备份：1-上传元数据：还未上传元数据(无globalID)
        mSyncManager.backUpLocalChangedGroupInfoMetaData(requestSource, BACK_UP_STEP0_2, object : IBackUpListener {
            override fun onStartUploadMetaData() {
                mSyncBackUpState = RecordSyncState.BACKUP_META_DATA_START
            }

            override fun onUploadMetaDataFinish(successData: List<CloudBackupResponseRecord>, errorData: List<CloudBackupResponseError>) {
                mSyncBackUpState = if (errorData.isNullOrEmpty()) RecordSyncState.BACKUP_META_DATA_SUCCESS else RecordSyncState.BACKUP_META_DATA_FAIL
                canContinueProcessCode = errorData.getOrNull(0)?.cloudKitError?.calFinalErrorCode() ?: canContinueProcessCode
            }

            override fun onFinish(stepValue: String, cloudKitError: CloudSyncResult) {
                // step0_2 完成 或 失败且不影响
                if (cloudKitError.isSuccess() || !handleBackUpMetaDataErrorContinueOrEnd(stepValue, tryCount, cloudKitError, requestSource)) {
                    doBackUpStep1(lastSyncState, requestSource = requestSource)
                }
            }
        })
    }


    /**
     * 备份STEP1 上传元数据（无globalId,有fileId）
     * @param tryCount 已重试次数，默认0
     * @param requestSource
     */
    private fun doBackUpStep1(lastSyncState: Int?, tryCount: Int = 0, requestSource: CloudBackupRequestSource) {
        DebugUtil.i(TAG, "doBackUpStep1 $requestSource, tryCount $tryCount", true)
        CloudStaticsUtil.addCloudLog(TAG, "doBackUpStep1,requestSource=$requestSource,lastSyncState=$lastSyncState,tryCount=$tryCount")
        // ui同步中状态,
        if (lastSyncState == null) {
            changeUiQueryOrSyncingState(SyncErrorCode.UI_STATE_SYNCING)
        }
        /*历史遗留问题：mimetype为空的脏数据，查出并更新mime type*/
        val checkedCount = PrefUtil.getInt(context, CHECK_ISSUE_MIME_TYPE_KEY, 0)
        if (checkedCount < CHECK_ISSUE_MIME_TYPE_MAX_COUNT) {
            PrefUtil.putInt(context, CHECK_ISSUE_MIME_TYPE_KEY, (checkedCount + 1))
            RecorderDBUtil.getInstance(context).checkAndUpdateRecordsMimeTypeIssue()
        }

        // 备份：1-上传元数据：已上传文件(有fileId)，还未上传元数据(无globalID)
        mSyncManager.backUpUploadedFileMetaData(requestSource, BACK_UP_STEP1, object : IBackUpListener {
            override fun onStartUploadMetaData() {
                mSyncBackUpState = RecordSyncState.BACKUP_META_DATA_START
            }

            override fun onUploadMetaDataFinish(successData: List<CloudBackupResponseRecord>, errorData: List<CloudBackupResponseError>) {
                mSyncBackUpState = if (errorData.isNullOrEmpty()) RecordSyncState.BACKUP_META_DATA_SUCCESS else RecordSyncState.BACKUP_META_DATA_FAIL
                canContinueProcessCode = errorData?.getOrNull(0)?.cloudKitError?.calFinalErrorCode() ?: canContinueProcessCode
            }

            override fun onFinish(stepValue: String, cloudKitError: CloudSyncResult) {
                // step1 完成 或 失败且不影响
                if (cloudKitError.isSuccess() || !handleBackUpMetaDataErrorContinueOrEnd(stepValue, tryCount, cloudKitError, requestSource)) {
                    doBackUpStep2(requestSource = requestSource)
                }
            }
        })
    }

    /**
     * 备份STEP2
     * @param tryCount 已重试次数，默认0
     * @param requestSource
     */
    private fun doBackUpStep2(tryCount: Int = 0, requestSource: CloudBackupRequestSource) {
        DebugUtil.i(TAG, "doBackUpStep2 $requestSource, tryCount $tryCount", true)
        CloudStaticsUtil.addCloudLog(TAG, "doBackUpStep2,tryCount=$tryCount")
        // 备份：2-上传元数据：已上传文件(有fileId)且上传过元数据(有globalID)，本地有变更(用户手动变更or恢复解冲突)
        mSyncManager.backUpLocalChangedMetaData(requestSource, BACK_UP_STEP2,
            object : IBackUpListener {
                override fun onStartUploadMetaData() {
                    mSyncBackUpState = RecordSyncState.BACKUP_META_DATA_START
                }

                override fun onUploadMetaDataFinish(successData: List<CloudBackupResponseRecord>, errorData: List<CloudBackupResponseError>) {
                    mSyncBackUpState =
                        if (errorData.isNullOrEmpty()) RecordSyncState.BACKUP_META_DATA_SUCCESS else RecordSyncState.BACKUP_META_DATA_FAIL
                    canContinueProcessCode = errorData?.getOrNull(0)?.cloudKitError?.calFinalErrorCode() ?: canContinueProcessCode
                }

                override fun onFinish(stepValue: String, cloudKitError: CloudSyncResult) {
                    if (cloudKitError.isSuccess() || !handleBackUpMetaDataErrorContinueOrEnd(stepValue, tryCount, cloudKitError, requestSource)) {
                        doBackUpStep3(requestSource = requestSource)
                    }
                }
            })
    }

    /**
     * 备份STEP3
     * @param tryCount 已重试次数，默认0
     * @param requestSource
     */
    private fun doBackUpStep3(tryCount: Int = 0, requestSource: CloudBackupRequestSource) {
        DebugUtil.i(TAG, "doBackUpStep3 $requestSource, tryCount $tryCount", true)
        CloudStaticsUtil.addCloudLog(TAG, "doBackUpStep3,tryCount=$tryCount")
        // 备份：3-批量上传文件--上传元数据
        mSyncManager.backUpUnUploadFileData(requestSource, BACK_UP_STEP3, object : IBackUpListener {
            override fun onStartUploadFile() {
                mSyncBackUpState = RecordSyncState.BACKUP_FILE_START
            }

            override fun onUploadFileFinish(errorFileList: List<RecordTransferFile>?) {
                mSyncBackUpState = if (errorFileList.isNullOrEmpty()) RecordSyncState.BACKUP_FILE_SUCCESS else RecordSyncState.BACKUP_FILE_FAIL
            }

            override fun onStartUploadMetaData() {
                mSyncBackUpState = RecordSyncState.BACKUP_META_DATA_START
            }

            override fun onUploadMetaDataFinish(successData: List<CloudBackupResponseRecord>, errorData: List<CloudBackupResponseError>) {
                mSyncBackUpState =
                    if (errorData.isNullOrEmpty()) RecordSyncState.BACKUP_META_DATA_SUCCESS else RecordSyncState.BACKUP_META_DATA_FAIL
            }

            /**
             * 上传元数据
             */
            override fun onFinish(stepValue: String, cloudKitError: CloudSyncResult) {
                // 如果部分元数据上传失败，同步也结束了，这里没有重试逻辑
                notifySyncResult(cloudKitError)
                mSyncBackUpState = RecordSyncState.BACKUP_COMPLETED
            }
        })
    }


    /**
     * UI querying、syncing状态变化
     */
    private fun changeUiQueryOrSyncingState(state: Int) {
        DebugUtil.i(TAG, "changeUiQueryOrSyncingState $state", true)

        TipStatusManager.updateSyncResult(CloudSyncResult.createResult(state, null))
        notifyCloudAppSyncState(CloudSyncState.START)
    }

    /**
     * 通知UI更新状态
     */
    private fun notifySyncResult(cloudSyncResult: CloudSyncResult) {
        DebugUtil.i(TAG, "notifySyncResult  $cloudSyncResult, isRecoveryEnter $isRecoveryEnter, canContinueProcessCode $canContinueProcessCode", true)
        cloudSyncResult.enterByRecovery = isRecoveryEnter
        cloudSyncResult.batchErrorCode = canContinueProcessCode

        TipStatusManager.updateSyncResult(cloudSyncResult)
        notifyCloudAppSyncState(CloudSyncState.FINISH)
//        GroupInfoManager.getInstance(BaseApplication.getAppContext()).checkAndResetRecordsGroupInfoAfterSync()
    }

    private fun retryBackupMetaData(step: String, newTryCount: Int, requestSource: CloudBackupRequestSource) {
        CloudStaticsUtil.addCloudLog(TAG, "retryBackupMetaData, step-$step,newTryCount=$newTryCount,requestSource=${requestSource.type}")
        when (step) {
            BACK_UP_STEP0_1 -> doBackUpGroupInfoStep01(SyncErrorCode.UI_STATE_SYNCING, newTryCount, requestSource)
            BACK_UP_STEP0_2 -> doBackUpGroupInfoStep02(SyncErrorCode.UI_STATE_SYNCING, newTryCount, requestSource)
            BACK_UP_STEP1 -> doBackUpStep1(SyncErrorCode.UI_STATE_SYNCING, newTryCount, requestSource)
            BACK_UP_STEP2 -> doBackUpStep2(newTryCount, requestSource)
            BACK_UP_STEP3 -> doBackUpStep3(newTryCount, requestSource)
        }
    }

    /**
     * 处理上传元数据结果
     * @return true: 已拦截处理了，不要再继续了
     * @return false: 可继续下一步流程
     */
    private fun handleBackUpMetaDataErrorContinueOrEnd(
        step: String,
        tryCount: Int,
        cloudKitError: CloudSyncResult,
        requestSource: CloudBackupRequestSource,
    ): Boolean {
        DebugUtil.e(TAG, "doBackUpDataStep $step error $cloudKitError ,tryCount $tryCount")
        // 云数据上传失败，且错误码是可以重试
        if (cloudKitError.errorFromMetaData() && cloudKitError.isCanTryCode()) {
            // 上传元数据失败，且可以重试错误码：满足重试条件-重试，不满足就继续下一步流程，不结束
            if (cloudKitError.isCanTry(tryCount, META_DATA_MAX_TRY_COUNT)) {
                delay(cloudKitError.cloudKitError?.delayRetryTime ?: 0)
                retryBackupMetaData(step, (tryCount + 1), requestSource)
                return true
            }
            return false
        }

        return when (cloudKitError.errorCode) {
            SyncErrorCode.RESULT_SUCCESS -> false
            SyncErrorCode.ERROR_RECORD_NEED_FETCH -> {
                mSyncBackUpState = RecordSyncState.BACKUP_COMPLETED
                // need fetch，云端有新数据需要拉下来
                doRecoveryImpl(SyncErrorCode.UI_STATE_SYNCING, false, CloudRecoveryRequestSource.NEED_FETCH)
                true
            }
            else -> {
                notifySyncResult(cloudKitError)
                // 无下一步操作，需在notifySyncResult后复位，否则可能影响 isRecoveryEnter 准确性
                mSyncBackUpState = RecordSyncState.BACKUP_COMPLETED
                true
            }
        }
    }

    private fun checkCanDoSync(): Boolean {
        if (mSyncRecoveryState != RecordSyncState.RECOVERY_COMPLETED) {
            // 已有恢复再走
            DebugUtil.e(TAG, "已存在恢复流程 state:$mSyncRecoveryState， back state:$mSyncBackUpState")
            return false
        } else if (mSyncBackUpState != RecordSyncState.BACKUP_COMPLETED) {
            // 再走备份流程
            DebugUtil.e(TAG, "已存在备份流程 state:$mSyncBackUpState，recovery state:$mSyncRecoveryState")
            return false
        }

        return true
    }

    private fun delay(mills: Long) {
        try {
            Thread.sleep(if (mills > 0) mills else 5000)
        } catch (e: Exception) {
            DebugUtil.e(TAG, "back up try error $e")
        }
    }

    /**
     * 云服务app需要展示业务方同步的进度
     * 告知云服务APP 同步状态-开始-结束
     */
    private fun notifyCloudAppSyncState(state: CloudSyncState) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            CloudSyncManager.getInstance().setSyncState(state)
        }
    }

    /**
     * 收到push消息，恢复完成后，是否需要备份
     */
    private fun needBackUpWhenPush() = true

    fun release() {
        DebugUtil.i(TAG, "release")
        CloudSyncAgent.release()
        reset()
    }

    fun reset() {
        mSyncBackUpState = RecordSyncState.BACKUP_COMPLETED
        mSyncRecoveryState = RecordSyncState.RECOVERY_COMPLETED
        isRecoveryEnter = false
        canContinueProcessCode = 0
    }
}