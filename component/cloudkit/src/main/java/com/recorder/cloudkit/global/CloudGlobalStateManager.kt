/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: CloudGlobalStateManager
 * Description:
 * Version: 1.0
 * Date: 2024/11/27
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/11/27 1.0 create
 */

package com.recorder.cloudkit.global

import android.app.Activity
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.dialog.COUIRotatingDialogBuilder
import com.heytap.cloudkit.libcommon.netrequest.ICloudResponseCallback
import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.heytap.cloudkit.libsync.cloudswitch.bean.SwitchState
import com.heytap.cloudkit.libsync.ext.CloudSyncManager
import com.heytap.cloudkit.libsync.service.FunctionScopeRsp
import com.recorder.cloudkit.R
import com.recorder.cloudkit.SyncTriggerManager.Companion.getInstance
import com.recorder.cloudkit.sync.CloudSynStateHelper
import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.modulerouter.cloudkit.CloudSwitchState
import com.soundrecorder.modulerouter.cloudkit.ICloudGlobalStateCallBack
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 云服务全球一体化需求
 * PRD:https://odocs.myoas.com/docs/plqxgIzlOQcTrD1n
 * 技术方案：https://odocs.myoas.com/docs/XKq4MWwM7PH50BkN
 */
object CloudGlobalStateManager {
    const val GLOBAL_ON_ENABLE = "global_on_enable"
    const val GLOBAL_ON_ACCOUNT_DISABLE = "global_on_account_disable"
    const val GLOBAL_ON_DEVICE_DISABLE = "global_on_device_disable"
    const val GLOBAL_OFF = "global_off"
    const val GLOBAL_ERROR = "global_error"
    const val SERVER_ERROR_502 = 502
    const val SERVER_ERROR_503 = 503
    const val SERVER_ERROR_504 = 504
    private const val TAG = "CloudGlobalStateManager"
    private const val ERROR_CODE_NETWORK = 2
    private val serverErrorCode = intArrayOf(SERVER_ERROR_502, SERVER_ERROR_503, SERVER_ERROR_504)

    /**一体化状态*/
    @Volatile
    private var sRecordGlobalState: String? = null

    /**一体化是否支持云同步，显示云同步入口，默认内销true，外销false，需配合regionSupport才真正可以进行云同步流程*/
    @Volatile
    private var sIsCloudGlobalSupport = getCloudGlobalSupportDefaultValue()
    private var globalCallbacks: CopyOnWriteArrayList<ICloudGlobalStateCallBack> = CopyOnWriteArrayList()

    /**
     * 获取云同步全球一体化开启状态和功能范围
     * 必须在init完成之后再调用此方法, 不管同步开关是否开启都需要去调用
     * 必须在已登录状态+支持云同步region下调用
     * 全球一体化接口，根据加账号注册地和设备打点地判断云服务是否可以, 全球一体化后其他所有同步行为都必须依赖这个接口
     * @param showErrorTip 接口错误是否根据错误码提示用户
     * @param resultFun 接口成功回调
     * @param errorFun 接口失败回调
     */

    @JvmStatic
    fun judgeOCloudGlobalState(showErrorTip: Boolean, callback: ICloudGlobalStateCallBack?) {
        val lastSupport = sIsCloudGlobalSupport
        if (!CloudSynStateHelper.isRegionCloudSupport()) { //拦截不支持云同步的国家
            sIsCloudGlobalSupport = false
            DebugUtil.w(TAG, "judgeOCloudState return by region not support")
            onSuccessCallback(lastSupport, callback)
            return
        }
        CoroutineScope(Dispatchers.IO).launch {
            //CloudSynStateHelper.isLoginFromCache()可能耗时，故整体放IO.
            if (!CloudSynStateHelper.isLoginFromCache()) { //拦截未登录
                reset()
                DebugUtil.w(TAG, "judgeOCloudState return by not login")
                withContext(Dispatchers.Main) {
                    onSuccessCallback(lastSupport, callback)
                }
                return@launch
            }

            CloudSyncManager.getInstance()
                .judgeOCloudState(object : ICloudResponseCallback<FunctionScopeRsp> {
                    override fun onSuccess(result: FunctionScopeRsp) {
                        DebugUtil.i(TAG, "judgeOCloudState result $result")
                        if (result.globalEnabled) { //一体化开启，读取配置信息
                            handleGlobalState(result.ocloudRegionState, lastSupport)
                        } else {
                            /**全球一体化功能未开启，默认老逻辑，根据设备打点地显示对应的功能(内销支持，外销不支持)*/
                            sIsCloudGlobalSupport = getCloudGlobalSupportDefaultValue()
                            sRecordGlobalState = GLOBAL_OFF
                        }
                        onSuccessCallback(lastSupport, callback)
                    }

                    override fun onError(error: CloudKitError) {
                        DebugUtil.d(TAG, "judgeOCloudState error $error")
                        handleError(showErrorTip, error)
                        onErrorCallback(callback)
                    }
                })
        }
    }

    @JvmStatic
    fun registerStateChangeCallBack(callback: ICloudGlobalStateCallBack) {
        if (!globalCallbacks.contains(callback)) {
            globalCallbacks.add(callback)
        }
    }

    @JvmStatic
    fun unRegisterStateChangeCallBack(callback: ICloudGlobalStateCallBack?) {
        if (globalCallbacks.contains(callback)) {
            globalCallbacks.remove(callback)
        }
    }

    private fun onSuccessCallback(lastSupport: Boolean, callback: ICloudGlobalStateCallBack?) {
        val changed = lastSupport != sIsCloudGlobalSupport
        callback?.onSuccess(changed, sIsCloudGlobalSupport, sRecordGlobalState)
        if (globalCallbacks.isNotEmpty()) {
            globalCallbacks.forEach {
                it.onSuccess(changed, sIsCloudGlobalSupport, sRecordGlobalState)
            }
        }
    }

    private fun onErrorCallback(callback: ICloudGlobalStateCallBack?) {
        callback?.onFailure()
        if (globalCallbacks.isNotEmpty()) {
            globalCallbacks.forEach {
                it.onFailure()
            }
        }
    }

    /**根据CloudKitError的信息提示用户，
     * 服务器异常502、503、504，通过subServerErrorCode判断： 提示：服务繁忙，请稍后重试
     * 客户端网络异常，通过bizErrorCode=2判断：网络异常，请检查网络后重试
     * */
    @JvmStatic
    private fun handleError(showErrorTip: Boolean, error: CloudKitError) {
        sRecordGlobalState = GLOBAL_ERROR
        sIsCloudGlobalSupport = getCloudGlobalSupportDefaultValue()

        if (!showErrorTip) {
            return
        }
        if (error.subServerErrorCode in serverErrorCode) {
            ToastManager.showShortToast(BaseApplication.getAppContext(), R.string.cloudkit_server_error)
        } else if (error.bizErrorCode == ERROR_CODE_NETWORK) {
            ToastManager.showShortToast(BaseApplication.getAppContext(), R.string.cloudkit_net_error)
        } else {
            DebugUtil.w(TAG, "handleError other code $error")
        }
    }

    @JvmStatic
    fun isInitSuccess(): Boolean = (sRecordGlobalState != null) && (sRecordGlobalState != GLOBAL_ERROR)

    /**
     * 是否能显示云同步开关
     */
    @JvmStatic
    fun canShowSyncSwitch(): Boolean = sIsCloudGlobalSupport

    /**
     * 是否可以走云同步流程
     */
    @JvmStatic
    fun canDoSyncProcess(): Boolean = sIsCloudGlobalSupport && (sRecordGlobalState == GLOBAL_ON_ENABLE || sRecordGlobalState == GLOBAL_OFF)

    fun stateToErrorCode(): Int {
        return when (sRecordGlobalState) {
            GLOBAL_ON_ACCOUNT_DISABLE -> SyncErrorCode.CODE_ACCOUNT_DISABLE
            GLOBAL_ON_DEVICE_DISABLE -> SyncErrorCode.CODE_DEVICE_DISABLE
            else -> SyncErrorCode.RESULT_SUCCESS
        }
    }

    @JvmStatic
    fun reset() {
        DebugUtil.i(TAG, "reset")
        sRecordGlobalState = null
        sIsCloudGlobalSupport = getCloudGlobalSupportDefaultValue()
    }

    @JvmStatic
    private fun handleGlobalState(regionState: String?, lastSupport: Boolean) {
        when (regionState) {
            /**显示国内完整云同步功能*/
            FunctionScopeRsp.ENABLE -> {
                sRecordGlobalState = GLOBAL_ON_ENABLE
                sIsCloudGlobalSupport = true
            }
            /**该账号不允许使用云服务*/
            FunctionScopeRsp.ACCOUNT_DISABLED -> {
                sRecordGlobalState = GLOBAL_ON_ACCOUNT_DISABLE
                sIsCloudGlobalSupport = false
                if (lastSupport) {
                    disableCloudSync()
                }
            }
            /**该设备不允许使用云服务*/
            FunctionScopeRsp.DEVICE_DISABLED -> {
                sRecordGlobalState = GLOBAL_ON_DEVICE_DISABLE
                sIsCloudGlobalSupport = false
                if (lastSupport) {
                    disableCloudSync()
                }
            }
        }
    }

    @JvmStatic
    private fun disableCloudSync() {
        getInstance(BaseApplication.getAppContext()).trigStopSyncForLoginOut(false)
        if (SwitchState.CLOSE.state != CloudSynStateHelper.getSwitchState(false)) {
            CloudSynStateHelper.setSyncSwitch(CloudSwitchState.CLOSE, false)
        }
    }

    @JvmStatic
    private fun getCloudGlobalSupportDefaultValue(): Boolean {
        return !BaseUtil.isEXP()
    }

    @JvmStatic
    fun showErrorDialog(activity: Activity?, state: String?, buttonListener: (() -> Unit)? = null): AlertDialog? {
        activity ?: return null
        DebugUtil.i(TAG, "showErrorDialog,state=$state")
        return when (state) {
            GLOBAL_ON_ACCOUNT_DISABLE, GLOBAL_ON_DEVICE_DISABLE -> doShowErrorDialog(activity, state, buttonListener)

            GLOBAL_OFF -> {
                /**外销一体化off兜底弹窗*/
                if (BaseUtil.isEXP() && !sIsCloudGlobalSupport) {
                    doShowErrorDialog(activity, GLOBAL_ON_DEVICE_DISABLE, buttonListener)
                } else {
                    null
                }
            }

            else -> null
        }
    }

    private fun doShowErrorDialog(activity: Activity, state: String, buttonListener: (() -> Unit)? = null): AlertDialog? {
        var dialog: AlertDialog? = null
        val observer = object : DefaultLifecycleObserver {
            override fun onDestroy(owner: LifecycleOwner) {
                super.onDestroy(owner)
                dialog?.dismiss()
                owner.lifecycle.removeObserver(this)
            }
        }
        val message = if (state == GLOBAL_ON_ACCOUNT_DISABLE) {
            R.string.cloudkit_global_account_disable_content
        } else {
            R.string.cloudkit_global_device_disable_content
        }
        if (activity is LifecycleOwner) {
            activity.lifecycle.addObserver(observer)
        }
        return COUIAlertDialogBuilder(activity, com.support.dialog.R.style.COUIAlertDialog_Bottom)
            .setBlurBackgroundDrawable(true)
            .setPositiveButton(R.string.know) { _, _ ->
                buttonListener?.invoke()
            }.setTitle(message).setCancelable(false).setOnDismissListener {
                if (activity is LifecycleOwner) {
                    activity.lifecycle.removeObserver(observer)
                }
            }.show().apply {
                dialog = this
            }
    }

    @JvmStatic
    fun showLoadingDialog(activity: Activity?): AlertDialog? {
        activity ?: return null
        DebugUtil.i(TAG, "showLoadingDialog")
        var dialog: AlertDialog? = null
        val observer = object : DefaultLifecycleObserver {
            override fun onDestroy(owner: LifecycleOwner) {
                super.onDestroy(owner)
                dialog?.dismiss()
                owner.lifecycle.removeObserver(this)
            }
        }
        dialog = COUIRotatingDialogBuilder(activity, activity.getString(R.string.cloudkit_switch_is_opening))
            .setBlurBackgroundWindow(true)
            .setDismissListener {
                if (activity is LifecycleOwner) {
                    activity.lifecycle.removeObserver(observer)
                }
            }.show()
        dialog.setCancelable(false)
        if (activity is LifecycleOwner) {
            activity.lifecycle.addObserver(observer)
        }
        return dialog
    }
}