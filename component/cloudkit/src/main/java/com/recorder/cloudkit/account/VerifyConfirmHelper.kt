/*********************************************************************
 * * Copyright (C), 2024-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : VerifyConfirmHelper
 * * Description : account second confirm about cloud sync
 * * Version     : 1.0
 * * Date        : 2024/12/10
 * * Author      : ********
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.recorder.cloudkit.account

import android.app.Activity
import com.platform.usercenter.account.ams.AcAccountConfig
import com.platform.usercenter.account.ams.AcAccountManager
import com.platform.usercenter.account.ams.apis.AcCallback
import com.platform.usercenter.account.ams.apis.beans.AcAccountToken
import com.platform.usercenter.account.ams.apis.beans.AcApiResponse
import com.platform.usercenter.account.ams.ipc.ResponseEnum
import com.platform.usercenter.sdk.verifysystembasic.AcVerifyAgent
import com.platform.usercenter.sdk.verifysystembasic.data.AcOperateType
import com.platform.usercenter.sdk.verifysystembasic.data.VerifyParam
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.AppUtil
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.base.utils.OS12FeatureUtil
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.cloudkit.VerifyCallBack
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

object VerifyConfirmHelper {

    private const val TAG = "VerifyConfirmHelper"

    /*以下的参数是根据包名在开发平台生成的,获取录音参数
      https://odocs.myoas.com/docs/vVqRVeDNwgFxPNqy
      可查看文档中开放平台的章节
    */
    /*
    private const val APP_ID = "30035557"
    private const val APP_K = "ead963094bc0406197ba1ad2fe6a26f2"
    private const val APP_S = "d184c8315c244d34b774bcb126cc7d12"
    private const val VERIFY_BUSINESS_ID = "b4dcf33966214d9aa0a08a7557535b6f"
     */

    /*测试环境*/
    private const val APP_ID = "********"
    private const val APP_K = "35194ab5d64e4920a6c931c21a9da965"
    private const val APP_S = "31f5054cb64f4fadb581e110c6314798"
    private const val VERIFY_BUSINESS_ID = "1b9322a55dc1465c9a505c900d06ee5e"

    // 帐号支持二次校验的最低版本号
    private const val PKG_ACCOUNT = "com.oplus.account"
    private const val ACCOUNT_SUPPORT_VERIFY_VERSION = 904000L

    private val cloudKitApi by lazy {
        Injector.injectFactory<CloudKitInterface>()
    }

    fun init() {
        val config: AcAccountConfig = AcAccountConfig.Builder()
            .setAppId(APP_ID)
            .setAppKey(APP_K)
            .create()
        AcAccountManager.init(BaseApplication.getAppContext(), config)
        DebugUtil.d(TAG, "AcAccountManager init.")
    }

    /**
     * 场景：开启同步开关
     * 内销，Android SDK >= Android13
     * ColorOS版本 > 13.2，sp中的ssoid为空或者sp中的ssoid与缓存中的ssoid不同
     * 账号APK版本 >= 9.0
     */
    private fun shouldCheckVerify(): Boolean {
        kotlin.runCatching {
            if (BaseUtil.isEXP() || !BaseUtil.isAndroidTOrLater) {
                DebugUtil.i(TAG, "shouldCheckVerify() isExpApp OR not OS13.1")
                return false
            }
            if (!OS12FeatureUtil.isColorOS14OrLater() || false == cloudKitApi?.checkAccountIsVerified()) {
                DebugUtil.i(TAG, "shouldCheckVerify() ColorOSVersion <=13.2 OR ssoid sp is empty OR ssoid sp is different")
                return false
            }
            val ucVersion = AppUtil.getPkgVersionCode(BaseApplication.getAppContext(), PKG_ACCOUNT)
            DebugUtil.d(TAG, "shouldCheckVerify() ucVersion=$ucVersion")

            return ucVersion != -1L && ucVersion >= ACCOUNT_SUPPORT_VERIFY_VERSION
        }.onFailure {
            DebugUtil.e(TAG, "shouldCheckVerify() error=$it")
        }
        return false
    }

    fun checkLoginAndVerify(activity: Activity, callback: VerifyCallBack) {
        if (!shouldCheckVerify()) {
            DebugUtil.i(TAG, "checkLoginAndVerify() shouldCheckVerify false")
            callback.onSuccess()
            return
        }
        //必须在子线程执行，否则会异常
        CoroutineScope(Dispatchers.IO).launch {
            // 获取token
            val response = AcAccountManager.getClient(APP_ID).accountToken
            DebugUtil.d(TAG, "checkLoginAndVerify()")
            if (response.code == ResponseEnum.ERROR_NOT_AUTH.code) {
                DebugUtil.w(TAG, "checkLoginAndVerify() ERROR_NOT_AUTH")
                // 如果没有授权先调用授权
                AcAccountManager.getClient(APP_ID).login(
                    activity, false,
                    object : AcCallback<AcApiResponse<AcAccountToken>> {
                        override fun call(response: AcApiResponse<AcAccountToken>) {
                            checkAndVerify(response, activity, callback)
                        }
                    })
            } else {
                checkAndVerify(response, activity, callback)
            }
        }
    }

    private fun checkAndVerify(
        response: AcApiResponse<AcAccountToken>?,
        activity: Activity,
        callback: VerifyCallBack
    ) {
        val token = response?.data?.accessToken
        DebugUtil.d(
            TAG,
            "checkAndVerify() response: ${response?.code}, isSuccess=${response?.isSuccess()}, ${!token.isNullOrBlank()}, msg=${response?.msg}"
        )
        if (response?.isSuccess() == true && !token.isNullOrBlank()) {
            startVerify(response, token, activity, callback)
        } else {
            callback.onFail()
        }
    }

    private fun startVerify(response: AcApiResponse<AcAccountToken>?, token: String, activity: Activity,  callback: VerifyCallBack) {
        val param = VerifyParam.Builder()
            .userToken(token)
            .bizk(APP_K)
            .bizs(APP_S)
            .businessId(VERIFY_BUSINESS_ID)
            .appId(APP_ID)
            .operateType(AcOperateType.VERIFY_TYPE)
            .create()
        AcVerifyAgent.startVerifyForResult(activity, param) {
            DebugUtil.d(TAG, "startVerify resultData=$it")
            if (it?.code == ResponseEnum.SUCCESS.code) {
                val value = response?.data?.ssoid ?: ""
                PrefUtil.putString(activity, PrefUtil.KEY_VERIFIED_ACCOUNT_SSOID,  value)
                callback.onSuccess()
            } else {
                callback.onFail()
            }
        }
    }
}