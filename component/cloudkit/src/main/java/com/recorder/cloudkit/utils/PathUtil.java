package com.recorder.cloudkit.utils;

import static com.soundrecorder.base.utils.FileUtils.MAX_FILE_NAME_LENGTH;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Environment;
import android.text.TextUtils;

import java.io.File;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.FileUtils;
import com.soundrecorder.base.utils.TimeUtils;
import com.soundrecorder.common.databean.Record;


public class PathUtil {

    private static final String TAG = "PathUtil";
    private static final String MUSIC_LOWER_CASE = "music";

    public static String getRelativePathFroConcatedPath(String path) {
        String fileName = "";
        if (TextUtils.isEmpty(path)) {
            return fileName;
        }
        int index = path.lastIndexOf(File.separator);
        if ((index > 0) && ((path.length() - 1) > index)) {
            fileName = path.substring(0, index);
        }
        return fileName;
    }

    public static String getUploadConcatedPath(Record record) {
        StringBuilder sb = new StringBuilder();
        if (BaseUtil.isAndroidQOrLater()) {
            String relativePathWithMusic = record.getConcatRelativePath();
            String relativePathWithoutMusic = relativePathWithMusic.replace(Environment.DIRECTORY_MUSIC + File.separator, "");
            sb.append(relativePathWithoutMusic);
        } else {
            String absolutePath = record.getData();
            String parent = BaseUtil.getPhoneStorageDir(BaseApplication.getAppContext()) + File.separator;
            String relativePath = absolutePath.replace(parent, "");
            sb.append(relativePath);
        }
        DebugUtil.i(TAG, "getUploadRelativePath: " + sb);
        return sb.toString();
    }

    public static String getDownloadRelativePath(String concatedPath) {
        StringBuilder sb = new StringBuilder();
        String relativePathWithoutName = getRelativePathFroConcatedPath(concatedPath);
        if (!TextUtils.isEmpty(relativePathWithoutName)) {
            if (BaseUtil.isAndroidQOrLater() && !concatedPath.toLowerCase().startsWith(MUSIC_LOWER_CASE)) {
                sb.append(Environment.DIRECTORY_MUSIC).append(File.separator).append(relativePathWithoutName).append(File.separator);
            } else {
                sb.append(relativePathWithoutName).append(File.separator);
            }
        }
        DebugUtil.i(TAG, "getDownloadRelativePath: " + sb);
        return sb.toString();
    }

    public static String getDownloadDataPath(Context context, String relativePathFromCloud) {
        StringBuilder sb = new StringBuilder();
        if (!TextUtils.isEmpty(relativePathFromCloud)) {
            if (BaseUtil.isAndroidQOrLater()) {
                sb.append(BaseUtil.getPhoneStorageDir(context)).append(File.separator).append(Environment.DIRECTORY_MUSIC).append(File.separator).append(relativePathFromCloud);
            } else {
                sb.append(BaseUtil.getPhoneStorageDir(context)).append(File.separator).append(relativePathFromCloud);
            }
        }
        String dataPath = sb.toString();
        DebugUtil.i(TAG, "getDownloadDataPath: relativePathFromCloud: " + relativePathFromCloud);
        return dataPath;
    }

    public static String getNameFromPath(String path) {
        String fileName = "";
        if (TextUtils.isEmpty(path)) {
            return fileName;
        }
        int index = path.lastIndexOf(File.separator);
        if ((index > 0) && ((path.length() - 1) > index)) {
            fileName = path.substring(index + 1);
        }
        return fileName;
    }

    public static String getFilePath(String relativePath, String displayName) {
        if (TextUtils.isEmpty(relativePath) || TextUtils.isEmpty(displayName)) {
            return "";
        }
        String parent = BaseUtil.getPhoneStorageDir(BaseApplication.getAppContext()) + File.separator;
        return parent + relativePath + displayName;
    }

    /**
     * 云同步解冲突，重命名本地or云端文件名称
     *
     * @param record
     * @return
     */
    public static String getNewNameForSyncRecordConflict(Record record) {
        String formatTime = PathUtil.getNewNameSuffixByConflict(record.getDateCreated());
        // 解冲突后名称可能大于 50字，使用modifyTime
        if ((FileUtils.getFileNameNoEx(record.getDisplayName() + formatTime).length() >= MAX_FILE_NAME_LENGTH)) {
            formatTime = PathUtil.getNewNameSuffixByConflict(record.getDateModied());
        }

        return FileUtils.getNewDisplayName(record.getRelativePath(), record.getDisplayName(), formatTime);
    }

    /**
     * 在相同命名但又是不一样的录音文件进行云同步时，重命名修改，会在默认命名上加录音的创建时间，空格隔开
     *
     * @param date
     * @return
     */
    @SuppressLint("SimpleDateFormat")
    public static String getNewNameSuffixByConflict(Long date) {
        if (date == null || date <= 0) {
            return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        }
        return new SimpleDateFormat("yyyyMMddHHmmss").format(new Date(date * TimeUtils.TIME_ONE_SECOND));
    }

}
