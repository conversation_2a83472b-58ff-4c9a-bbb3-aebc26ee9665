/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: DownloadSyncUtil
 ** Description:
 ** Version: 1.0
 ** Date : 2019-07-11
 ** Author: huangyuanwang
 **
 ** v1.0, 2019-3-12, huangyuanwang, create
 ****************************************************************/

// OPLUS Java File Skip Rule:NestedBranchDepth
package com.recorder.cloudkit.sync.recovery;

import static com.soundrecorder.common.constant.DatabaseConstant.CONCAT_PROJECTION_STRING;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_GLOBAL_ID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_ID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_MD5;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_UUID;
import static com.soundrecorder.common.constant.RecordConstant.SYNC_STATUS_RECOVERY_FILE_SUC;
import static com.soundrecorder.common.constant.RecordConstant.SYNC_STATUS_RECOVERY_MEGADATA_SUC;
import static com.soundrecorder.common.constant.RecordConstant.SYNC_TYPE_RECOVERY;

import android.app.RecoverableSecurityException;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.net.Uri;
import android.os.Build;
import android.provider.MediaStore;
import android.text.TextUtils;

import com.google.gson.Gson;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


import com.heytap.cloudkit.libsync.metadata.helper.CloudRecoveryRequestSource;
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudMetaDataFileInfo;
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudMetaDataRecord;

import com.recorder.cloudkit.sync.bean.CloudGroupInfoField;
import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.FileUtils;
import com.soundrecorder.base.utils.MediaDataScanner;
import com.soundrecorder.common.buryingpoint.CloudStaticsUtil;
import com.soundrecorder.common.constant.RecordConstant;
import com.soundrecorder.common.databean.DirectRecordStatus;
import com.soundrecorder.common.databean.GroupInfo;
import com.soundrecorder.common.db.ConvertDeleteUtil;
import com.soundrecorder.common.db.GroupInfoDbUtil;
import com.soundrecorder.common.db.GroupInfoManager;
import com.soundrecorder.common.db.MediaDBUtils;
import com.soundrecorder.common.db.NoteDbUtils;
import com.soundrecorder.common.db.RecorderDBUtil;

import com.soundrecorder.common.databean.Record;

import com.recorder.cloudkit.sync.CloudSyncAgent;
import com.recorder.cloudkit.sync.SyncDataConstants;
import com.recorder.cloudkit.sync.bean.CloudRecordField;
import com.recorder.cloudkit.tipstatus.TipStatusManager;
import com.soundrecorder.common.db.CloudSyncRecorderDbUtil;
import com.recorder.cloudkit.utils.PathUtil;
import com.soundrecorder.common.utils.FileDealUtil;
import com.soundrecorder.common.utils.RecordFileChangeNotify;
import com.soundrecorder.common.utils.RecordModeUtil;

import androidx.annotation.RequiresApi;

public class DownloadSyncUtil {

    private static final String TAG = "DownloadSyncUtil";
    private static final int PAGE_SIZE_500 = 500;


    /**
     * 下载分组元数据成功，处理元数据冲突更新本地数据库
     *
     * @param requestSource
     * @param metaDataRecordList
     */
    public static boolean onServerDataSuccessForGroupInfoRecover(Context context,
                                                                 CloudRecoveryRequestSource requestSource,
                                                                 List<CloudMetaDataRecord> metaDataRecordList) {
        CloudStaticsUtil.addCloudLog(TAG, "onServerDataSuccessForGroupInfoRecover,requestSource=" + requestSource.getType()
                + ",dataSize=" + ((metaDataRecordList == null) ? 0 : metaDataRecordList.size()));
        boolean result = true;
        if ((metaDataRecordList != null) && (metaDataRecordList.size() > 0)) {
            Gson gson = new Gson();
            RecordFileChangeNotify recordFileChangeNotify = new RecordFileChangeNotify();
            // 云端新增or变更元数据
            ArrayList<GroupInfo> addOrUpdateList = null;
            // 云端彻底删除的元数据- 对应备份-文件加密
            ArrayList<GroupInfo> deleteRecords = null;
            // 云端删除至回收站的元数据 - 对应备份-录音删除
            ArrayList<GroupInfo> syncDeleteRecords = null;
            for (CloudMetaDataRecord metaDataRecord : metaDataRecordList) {
                if (CloudSyncAgent.getInstance().isManualStop() || !TipStatusManager.isCloudOn()) {
                    result = false;
                    CloudStaticsUtil.addCloudLog(TAG, "onServerDataSuccessForGroupInfoRecover, end by stop or cloud off");
                    break;
                }
                final GroupInfo webGroupInfo = toGroupInfo(gson, metaDataRecord);
                if (webGroupInfo == null) {
                    DebugUtil.e(TAG, "onServerDataSuccessForGroupInfoRecover : to group info null ");
                    continue;
                }
                final String globalId = webGroupInfo.getMGroupGlobalId();
                if (TextUtils.isEmpty(globalId)) {
                    continue;
                }
                switch (metaDataRecord.getSysStatus()) {
                    case SyncDataConstants.SYS_STATUS_NORMAL:
                        DebugUtil.i(TAG, "update records: \n" + webGroupInfo + "\n packet: " + metaDataRecord, true);
                        CloudStaticsUtil.addCloudLog(TAG, "onServerDataSuccessForGroupInfoRecover, SYS_STATUS_NORMAL,"
                                + webGroupInfo.getMGroupName() + ", sysRecordId=" + webGroupInfo.getMUuId());
                        addOrUpdateList = new ArrayList<>();
                        addOrUpdateList.add(webGroupInfo);
                        addOrUpdateDownloadSyncDataForGroupInfo(context, addOrUpdateList);
                        break;
                    case SyncDataConstants.SYS_STATUS_DELETED:
                        DebugUtil.i(TAG, "delete group: \n" + webGroupInfo + "\n packet: " + metaDataRecord, true);
                        syncDeleteRecords = new ArrayList<>();
                        syncDeleteRecords.add(webGroupInfo);
                        deleteDownloadSyncDataForGroupInfo(context, syncDeleteRecords);
                        break;
                    case SyncDataConstants.SYS_STATUS_RECYCLE:
                        DebugUtil.i(TAG, "delete group to recycle: \n" + webGroupInfo + "\n packet: " + metaDataRecord, true);
                        syncDeleteRecords = new ArrayList<>();
                        syncDeleteRecords.add(webGroupInfo);
                        deleteDownloadSyncDataForGroupInfo(context, syncDeleteRecords);
                        break;
                    default:
                        DebugUtil.i(TAG, "unknown type \n" + metaDataRecord.getSysStatus() + "\n packet: " + metaDataRecord, true);
                        CloudStaticsUtil.addCloudLog(TAG, "onServerDataSuccessForGroupInfoRecover, otherStatus=" + metaDataRecord.getSysStatus());
                        break;
                }
            }
        }

        return result;
    }

    /**
     * 下载元数据成功，处理元数据冲突更新本地数据库
     *
     * @param requestSource
     * @param metaDataRecordList
     * @throws Exception
     */
    @RequiresApi(api = Build.VERSION_CODES.Q)
    public static boolean onServerDataSuccessForRecover(Context context, CloudRecoveryRequestSource requestSource, List<CloudMetaDataRecord> metaDataRecordList) throws Exception {
        CloudStaticsUtil.addCloudLog(TAG, "onServerDataSuccessForRecover,requestSource=" + requestSource.getType()
                + ",dataSize=" + ((metaDataRecordList == null) ? 0 : metaDataRecordList.size()));
        boolean result = true;
        if ((metaDataRecordList != null) && (metaDataRecordList.size() > 0)) {
            Gson gson = new Gson();
            RecordFileChangeNotify recordFileChangeNotify = new RecordFileChangeNotify();
            // 云端新增or变更元数据
            ArrayList<Record> addOrUpdateList;
            // 云端彻底删除的元数据- 对应备份-文件加密
            ArrayList<Record> deleteRecords;
            // 云端删除至回收站的元数据 - 对应备份-录音删除
            ArrayList<Record> syncDeleteRecords;
            for (CloudMetaDataRecord metaDataRecord : metaDataRecordList) {
                if (CloudSyncAgent.getInstance().isManualStop() || !TipStatusManager.isCloudOn()) {
                    result = false;
                    CloudStaticsUtil.addCloudLog(TAG, "onServerDataSuccessForRecover, end by stop or cloud off");
                    break;
                }
                final Record webRecord = toRecord(gson, metaDataRecord);
                if (webRecord == null) {
                    DebugUtil.e(TAG, "onServerAddOrUpdateForRecover : to record null ");
                    continue;
                }
                final String globalId = webRecord.getGlobalId();
                if (TextUtils.isEmpty(globalId)) {
                    continue;
                }
                switch (metaDataRecord.getSysStatus()) {
                    case SyncDataConstants.SYS_STATUS_NORMAL:
                        DebugUtil.i(TAG, "update records: \n" + webRecord + "\n packet: " + metaDataRecord, true);
                        CloudStaticsUtil.addCloudLog(TAG, "onServerDataSuccessForRecover,SYS_STATUS_NORMAL,"
                                + webRecord.getDisplayName() + ", sysRecordId=" + webRecord.getGlobalId());
                        addOrUpdateList = new ArrayList<>();
                        addOrUpdateList.add(webRecord);
                        processDownloadSyncUpdateMegaData(context, requestSource, addOrUpdateList, recordFileChangeNotify);
                        break;
                    case SyncDataConstants.SYS_STATUS_DELETED:
                        DebugUtil.i(TAG, "delete records: \n" + webRecord + "\n packet: " + metaDataRecord, true);
                        syncDeleteRecords = new ArrayList<>();
                        syncDeleteRecords.add(webRecord);
                        processDownloadSyncDeleteMegaData(context, requestSource, syncDeleteRecords, true, recordFileChangeNotify);
                        break;
                    case SyncDataConstants.SYS_STATUS_RECYCLE:
                        DebugUtil.i(TAG, "delete records to recycle: \n" + webRecord + "\n packet: " + metaDataRecord, true);
                        deleteRecords = new ArrayList<>();
                        deleteRecords.add(webRecord);
                        processDownloadSyncDeleteMegaData(context, requestSource, deleteRecords, false, recordFileChangeNotify);
                        break;
                    default:
                        DebugUtil.i(TAG, "unknown type \n" + metaDataRecord.getSysStatus() + "\n packet: " + metaDataRecord, true);
                        CloudStaticsUtil.addCloudLog(TAG, "onServerDataSuccessForRecover,otherStatus=" + metaDataRecord.getSysStatus());
                        break;
                }
            }
            if (recordFileChangeNotify != null) {
                recordFileChangeNotify.notifyBySendBroadcast(context);
            }
        }

        return result;
    }

    private static void addOrUpdateDownloadSyncDataForGroupInfo(Context context, List<GroupInfo> cloudGroupInfoList) {
        if (cloudGroupInfoList != null && !cloudGroupInfoList.isEmpty()) {
            for (int i = 0; i < cloudGroupInfoList.size(); i++) {
                GroupInfo cloudGroupInfo = cloudGroupInfoList.get(i);
                GroupInfo localGroupInfo = GroupInfoManager.getInstance(context).getLocalGroupInfoInDbByCloudGroupInfo(cloudGroupInfo);
                //根据云端对象查询到本地数据库有记录，则直接更新；否则直接插入
                if (localGroupInfo != null) {
                    //本地和云端 sysVersion相同，则以本地数据为准，因为本地数据数据可能有改动
                    if (localGroupInfo.getSysVersion() == cloudGroupInfo.getSysVersion()) {
                        DebugUtil.w(TAG, "local group info sysVersion is same with cloud");
                        continue;
                    }
                    /*如果有UUID相同的本地记录且被标记为脏状态，则以本地数据为准，跳过云同步下来数据状态的处理：
                     * （1）UUID相同表示是相同账号下的数；
                     * （2）切换账号后，即使分组名称相同，uuid必然不同；则强制使用云端相同分组名称对应的所有信息，
                     *     以云端分组信息为准
                     */
                    boolean bUuIdSame = localGroupInfo.getMUuId().equals(cloudGroupInfo.getMUuId());
                    if (bUuIdSame && localGroupInfo.getMDirty() != RecordConstant.RECORD_NOT_DIRTY) {
                        DebugUtil.w(TAG, "local group info is dirty, return to sync cloud status");
                        continue;
                    }
                    localGroupInfo.setMGroupGlobalId(cloudGroupInfo.getMGroupGlobalId());
                    localGroupInfo.setMGroupName(cloudGroupInfo.getMGroupName());
                    localGroupInfo.setMGroupColor(cloudGroupInfo.getMGroupColor());
                    localGroupInfo.setMUuId(cloudGroupInfo.getMUuId());
                    localGroupInfo.setSyncType(SYNC_TYPE_RECOVERY);
                    localGroupInfo.setSyncDownloadStatus(SYNC_STATUS_RECOVERY_MEGADATA_SUC);
                    localGroupInfo.setMDirty(RecordConstant.RECORD_NOT_DIRTY);
                    GroupInfoDbUtil.updateGroupInfo(context, localGroupInfo);
                } else {
                    cloudGroupInfo.setMDirty(RecordConstant.RECORD_NOT_DIRTY);
                    GroupInfoDbUtil.insertGroupInfo(context, cloudGroupInfo);
                }
            }
        }
    }

    private static void deleteDownloadSyncDataForGroupInfo(Context context, List<GroupInfo> cloudGroupInfoList) {
        if (cloudGroupInfoList != null && !cloudGroupInfoList.isEmpty()) {
            for (int i = 0; i < cloudGroupInfoList.size(); i++) {
                GroupInfo cloudGroupInfo = cloudGroupInfoList.get(i);
                GroupInfo localGroupInfo = GroupInfoDbUtil.getLocalGroupInfoInDbByCloudGroupInfo(context, cloudGroupInfo);
                //根据云端对象查询到本地数据库有记录，则直接删除
                if (localGroupInfo != null) {
                    GroupInfoDbUtil.deleteByUUId(context, localGroupInfo.getMUuId());
                }
            }
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.Q)
    private static void processDownloadSyncUpdateMegaData(Context context, CloudRecoveryRequestSource requestSource, List<Record> updateRecords, RecordFileChangeNotify recordFileChangeNotify) throws Exception {
        //0. 元数据的分组可能已不存在，则重置其未路径对应的分组
        if (updateRecords != null && !updateRecords.isEmpty()) {
            //再查一次分组表信息，以防因清理数据导致分组信息不存在，云端下载的分组信息被重置
            GroupInfoManager.getInstance(context).queryAllCustomGroupInfoList();
            for (int i = 0; i < updateRecords.size(); i++) {
                Record record = updateRecords.get(i);
                String currentGroupUUID = record.getGroupUuid();
                if (currentGroupUUID != null && !currentGroupUUID.isEmpty()
                        && GroupInfoDbUtil.isCustomGroup(currentGroupUUID)) {
                    boolean isGroupExist = GroupInfoManager.getInstance(context).checkGroupExistByUUID(currentGroupUUID);
                    if (!isGroupExist) {
                        GroupInfoManager.getInstance(context).resetGroupInfoForRecord(record);
                        DebugUtil.d(TAG, "processDownloadSyncUpdateMegaData, custom group was deleted, resetGroupInfoForRecordSync");
                    }
                }
            }
        }
        //1. process local data = data from cloud files
        updateExistFilePathData(context, updateRecords);
        //2. process local globalid = globalid from cloud files
        updateExistSameGlobalIdData(context, updateRecords, recordFileChangeNotify);
        //3. process update records according data and md5
        processDownloadUpdate(context, updateRecords, recordFileChangeNotify);
    }

    @RequiresApi(api = Build.VERSION_CODES.Q)
    private static void processDownloadSyncDeleteMegaData(Context context, CloudRecoveryRequestSource requestSource, List<Record> deleteRecord, boolean isSyncDelete, RecordFileChangeNotify recordFileChangeNotify) throws Exception {
        processDownloadDelete(context, requestSource, deleteRecord, isSyncDelete, recordFileChangeNotify);
    }

    @RequiresApi(api = Build.VERSION_CODES.Q)
    private static void processDownloadSyncMegaData(Context context, CloudRecoveryRequestSource requestSource, List<Record> updateRecords, List<Record> deleteRecords, List<Record> syncDeleteRecords) throws Exception {
        long time = System.currentTimeMillis();
        RecordFileChangeNotify recordFileChangeNotify = new RecordFileChangeNotify();
        DebugUtil.d(TAG, "syncDataDownload :" + "update size: " + updateRecords.size() + "---" + Arrays.toString(updateRecords.toArray()), true);
        //1. process local data = data from cloud files
        updateExistFilePathData(context, updateRecords);
        //2. process local globalid = globalid from cloud files
        updateExistSameGlobalIdData(context, updateRecords, recordFileChangeNotify);
        //3. process update records according data and md5
        processDownloadUpdate(context, updateRecords, recordFileChangeNotify);
        //4. process add records according data and md5
//        processDownloadUpdate(context, addRecords, recordFileChangeNotify);
        //5. process delete records according globalId, must processed after add and update
        processDownloadDelete(context, requestSource, deleteRecords, false, recordFileChangeNotify);
        processDownloadDelete(context, requestSource, syncDeleteRecords, true, recordFileChangeNotify);
        //6. notifyRecordingsFileChanged
        recordFileChangeNotify.notifyBySendBroadcast(context);
        DebugUtil.d(TAG, "syncDataDownload, end. cost time=" + (System.currentTimeMillis() - time), true);
    }


    /**
     * 从本地recorder.table和媒体库中查找相同云端文件路径数据
     * recorder.table：
     * 1.no globalId
     * 1.1 相同md5、size
     * 1.2 不同size or md5：插入新记录，insert  new record into recorder.table
     * 媒体库查找（recorder.db没找到的）：
     * size 不相同：插入新记录
     *
     * @param context
     * @param updateRecords
     * @throws RecoverableSecurityException
     */
    private static void updateExistFilePathData(Context context, List<Record> updateRecords) throws RecoverableSecurityException {
        long time = System.currentTimeMillis();
        // get net data
        List<String> existRelativeFilePathList = new ArrayList<>();
        List<String> existAbsoluteFilePathList = new ArrayList<>();
        Map<String, Record> existFileMap = new HashMap<>();
        if (updateRecords != null) {
            for (Record record : updateRecords) {
                existRelativeFilePathList.add(record.getConcatRelativePath());
                existFileMap.put(record.getConcatRelativePath(), record);
                existAbsoluteFilePathList.add(generateNewPathForWebRecord(context, record));
            }
        }
        if (existRelativeFilePathList.size() == 0) {
            return;
        }
        // get sync db data by paths
        List<Record> recordsInDbList = new ArrayList<>();
        int count = 0;
        int pageNob = 0;
        int pageSize = PAGE_SIZE_500;
        List<String> items = BaseUtil.getPageItems(existRelativeFilePathList, pageNob, pageSize);
        while ((items != null) && !items.isEmpty()) {
            count += items.size();
            StringBuilder builder = MediaDBUtils.getWhereForInKeyword(items.size(), CONCAT_PROJECTION_STRING);
            String[] selectionArgs = new String[items.size()];
            items.toArray(selectionArgs);
            List<Record> list = RecorderDBUtil.getRecordData(context, null, builder.toString(), selectionArgs, null);
            if ((list != null) && !list.isEmpty()) {
                recordsInDbList.addAll(list);
            }
            pageNob++;
            items = BaseUtil.getPageItems(existRelativeFilePathList, pageNob, pageSize);
        }
        DebugUtil.i(TAG, "updateExistFilePathData, refactor_db_batch count=" + count
                + ", sucessful=" + (count == existRelativeFilePathList.size()) +
                ",recordsInDbList.size " + recordsInDbList.size(), true);

        // process record which in sync db
        if (!recordsInDbList.isEmpty()) {
            for (Record recordDb : recordsInDbList) {
                Record recordCloud = existFileMap.remove(recordDb.getConcatRelativePath());
                if (!recordDb.hasGlobalId() && (recordCloud != null)) {
                    long dbFileSize = recordDb.getFileSize();
                    recordDb.checkMd5();
                    if ((dbFileSize != recordCloud.getFileSize()) || (!recordDb.sameMD5(recordCloud))) {
                        DebugUtil.v(TAG, "updateExistFilePathData, db name=" + recordDb.getDisplayName()
                                + "\n fileSize from db: " + recordDb.getFileSize() + ", fileSize from net: " + recordCloud.getFileSize()
                                + "\n " + "md5 from db: " + recordDb.getMD5() + ", md5 from net: " + recordCloud.getMD5(), true);
                        Uri recordMediaUri = MediaDBUtils.getMediaUriForRecord(recordDb);
                        if (recordMediaUri != null) {
                            dbFileSize = FileUtils.getFileSize(recordMediaUri);
                            if ((dbFileSize == recordCloud.getFileSize()) && recordDb.sameMD5(recordCloud)) {
                                DebugUtil.v(TAG, "updateExistFilePathData: md5 is same,  update realFileSize ", true);
                                RecorderDBUtil.getInstance(context).updateRealFileSizeOrMd5(recordDb, dbFileSize, recordDb.mMD5);
                            } else {
                                // 本地相同文件名称记录，文件size/md5不同：重命名本地文件名称--增加是以前是云端数据下载下载重命名的
                                String newDisplayName = PathUtil.getNewNameForSyncRecordConflict(recordDb);
                                boolean localRenameSuccess = MediaDBUtils.rename(recordMediaUri, newDisplayName) > 0;
                                if (localRenameSuccess) {
                                    // 文件重命名成功，更新本地db
                                    RecorderDBUtil.getInstance(context).updateDisplayNameByRecordId(String.valueOf(recordDb.getId()), newDisplayName, recordDb.getRecordType(), true);
                                }
                                // 云端数据插入到本地，需要更新picture_mark表格
                                boolean result = CloudSyncRecorderDbUtil.insertCloudMetadataForLocallyExistsFile(recordCloud);
                                DebugUtil.i(TAG, "renameLocalFile = " + localRenameSuccess + " insertCloudMetadata, result = " + result, true);
                                CloudStaticsUtil.addCloudLog(TAG, "updateExistFilePathData,insert cloud " + recordCloud.getDisplayName()
                                        + ",result=" + result + ",localFile rename to " + newDisplayName + " result=" + localRenameSuccess);
                            }
                        }
                    }
                }
            }
        }
        // process record which not in sync db
        for (Record recordCloud : existFileMap.values()) {
            Uri recordMediaUri = MediaDBUtils.getMediaUriForRecord(recordCloud);
            if (recordMediaUri != null) {
                Record recordMedia = RecorderDBUtil.getInstance(context).getRecordFromDBbyMediaUri(recordMediaUri);
                long realFileSize = FileUtils.getFileSize(recordMediaUri);
                if ((recordMedia != null) && (realFileSize != recordCloud.getFileSize())) {
                    // 重命名本地文件
                    String newName = PathUtil.getNewNameForSyncRecordConflict(recordMedia);
                    int renameLocalFile = MediaDBUtils.rename(recordMediaUri, newName);
                    if (renameLocalFile > 0) {
                        CloudStaticsUtil.addCloudLog(TAG, "updateExistFilePathData,"
                                + "insertOrUpdateNewRecord, " + newName + ",origin name=" + recordMedia.getDisplayName());
                        DirectRecordStatus directRecordStatus = new DirectRecordStatus(recordCloud.getDirectOn(),
                                recordCloud.getDirectTime());
                        RecorderDBUtil.getInstance(context).insertOrUpdateNewRecord(
                                recordMediaUri, null, null,
                                RecordModeUtil.getRecordTypeForMediaRecord(recordMedia), null, true,
                                directRecordStatus, null);
                    }
                    DebugUtil.v(TAG, "updateExistFilePathData, db name=" + recordCloud.getDisplayName() + "renameLocalFile " + renameLocalFile
                            + "\n fileSize from real file size : " + realFileSize + ", fileSize from net: " + recordCloud.getFileSize(), true);
                    //云端数据插入到本地，需要更新picture_mark表格
                    boolean result = CloudSyncRecorderDbUtil.insertCloudMetadataForLocallyExistsFile(recordCloud);
                    DebugUtil.i(TAG, "not in sync db" + recordCloud.toString() + " , result = " + result, true);
                    CloudStaticsUtil.addCloudLog(TAG, "updateExistFilePathData, insert cloud record " + result + "-" + recordCloud.getDisplayName());
                }
            }
        }
        DebugUtil.i(TAG, "updateExistFilePathData, end. cost time=" + (System.currentTimeMillis() - time), true);
    }

    /**
     * 查找本地recorder.table中存在云端相同globalId的数据
     * 1.本地、远端 sysVersion相同 -> ignore，以本地数据为准
     * 2.远端version>本地
     * 2.1 本地标记为脏数据(dirty)：db中文件存在+文件大小同云端相同： 清空本地db中的globalID
     * 3.本地标记非脏数据
     * 3.1文件名称不同+size/md5 同云端相同：重命名本地文件名称，标记待上传
     *
     * @param context
     * @param updateRecords
     * @param recordFileChangeNotify
     * @throws RecoverableSecurityException
     */
    @RequiresApi(api = Build.VERSION_CODES.Q)
    private static void updateExistSameGlobalIdData(Context context, List<Record> updateRecords, RecordFileChangeNotify recordFileChangeNotify) throws RecoverableSecurityException {
        long time = System.currentTimeMillis();
        List<Record> allData = new ArrayList<>();
        if ((updateRecords != null) && !updateRecords.isEmpty()) {
            allData.addAll(updateRecords);
        }
        if (allData.size() == 0) {
            return;
        }
        Map<String, Record> hashMap = new HashMap<>();
        StringBuilder builder = new StringBuilder();
        builder.append(COLUMN_NAME_GLOBAL_ID + " IN (");
        for (Record record : allData) {
            builder.append("\"").append(record.getGlobalId()).append("\"");
            builder.append(",");
            hashMap.put(record.mGlobalId, record);
        }
        if (builder.lastIndexOf(",") == builder.length() - 1) {
            builder.deleteCharAt(builder.length() - 1);
        }
        builder.append(")");
        try {
            List<Record> recordDblist = RecorderDBUtil.getRecordData(context, null, builder.toString(), null, null);
            if ((recordDblist != null) && (!recordDblist.isEmpty())) {
                DebugUtil.i(TAG, "updateExistSameGlobalIdData: same globalid in local db : " + recordDblist.size(), true);
                for (int i = 0; i < recordDblist.size(); i++) {
                    Record recordDb = recordDblist.get(i);
                    Record recordCloud = hashMap.get(recordDb.getGlobalId());
                    // 版本号相同，以本地数据为准，可能本地数据有改动
                    if ((recordCloud.sameSysVersion(recordDb)) || (recordDb.getConcatRelativePath().equalsIgnoreCase(recordCloud.getConcatRelativePath()))) {
                        DebugUtil.i(TAG, "updateExistSameGlobalIdData: same sysVersion : " + recordCloud.getSysVersion(), true);
                        continue;
                    }
                    /* 版本号不同，本地 < 云端
                     * globalid equal, path not equal:
                     *  1. user has rename the file in local and not sync to cloud imediaty (record db is dirty),
                     *  the original record from cloud recovered to local
                     * 2. user local data is clean(record db is not dirty) but not match with the newest verison on the cloud*/
                    DebugUtil.d(TAG, "updateExistSameGlobalIdData, local db : " + recordDb.getDisplayName() + "from cloud: "
                            + recordCloud.getDisplayName() + ", globalid: " + recordDb.getGlobalId(), true);
                    // 云端该条数据有改动，本地被设为私密了。被设为私密等同于删除，所以云端覆盖本地
                    if (recordDb.getSyncPrivateStatus() == RecordConstant.RECORD_PRIVETE_ENCRYPT) {
                        DebugUtil.i(TAG, "updateExistSameGlobalIdData,local record is  RECORD_PRIVETE_ENCRYPT");
                        // 本地私密，云端覆盖本地，本地老记录删除，新的会在后面逻辑中(processDownloadUpdate)insert
                        int deleteCount = RecorderDBUtil.deleteRecordData(context, COLUMN_NAME_ID + " = ?"
                                , new String[]{String.valueOf(recordDb.getId())});
                        CloudStaticsUtil.addCloudLog(TAG, "updateExistSameGlobalIdData, local record is encrypted, delete local "
                                + recordDb.getDisplayName() + ",deleteCount=" + deleteCount);
                        continue;
                    }
                    if (recordDb.isDirty()) {
                        if (recordDb.fileExistAndCheckSize() && recordDb.sameFileSize(recordCloud)) {
                            // 新数据插db放到processDownloadUpdate中(文件名称肯定变更了，相同文件名称的再上一步处理了)
                            boolean result = CloudSyncRecorderDbUtil.markAsWaitingToUploadForUpdateById(recordDb.getId(), true, true);
                            DebugUtil.d(TAG, "updateExistSameGlobalIdData, markAsWaitingToUploadForUpdateById : " + recordDb.getId(), true);
                            CloudStaticsUtil.addCloudLog(TAG, "updateExistSameGlobalIdData,recordDb is dirty,same size with cloud,"
                                    + "mark local record to new data, " + recordDb.getDisplayName() + ", result=" + result);
                        }
                    } else {
                        boolean samePath = recordDb.getConcatRelativePath().equalsIgnoreCase(recordCloud.getConcatRelativePath());
                        //not dirty , 本地数据保持云端一致
                        if (recordDb.fileExistAndCheckSize() && recordDb.sameFileSize(recordCloud) && recordDb.sameMD5(recordCloud) && !samePath) {
                            CloudStaticsUtil.addCloudLog(TAG, "updateExistSameGlobalIdData,local record not dirty, but different path");
                            renameLocalFileWhenSameRecord(context, recordDb, recordCloud, recordFileChangeNotify);
                        }
                    }
                }
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "updateExistSameGlobalIdData error.", e);
            if (e instanceof SecurityException) {
                throw e;
            }
        }
        DebugUtil.d(TAG, "updateExistSameMD5Data, end. cost time=" + (System.currentTimeMillis() - time), true);
    }

    /**
     * 云端数据check
     * 1.path 再本地存在该文件且size>0 - > 标记恢复结束
     * 2.path 再本地无or size=0 - >  标记需下载文件
     * 接着执行
     * 查询本地数据库：(path、md5相同的记录)
     * 1. 本地标记为脏数据(dirty)
     * 1.1 标记不同：本地存在该文件：md5、path相同，复制本地file重命名，插入db
     * 1.2 标记相同，更新本地 db -》end
     * 2.db查找相同uuid数据
     * 2.1 没找到 or 本地标记为dirty且名称不同，插入云端数据到本地
     * 2.2 本地标记为非脏数据， path不相同，md5、size同云端一致 ->rename 本地文件名称为云端名称、更新db
     *
     * @param context
     * @param updateRecords
     * @param recordFileChangeNotify
     * @throws Exception
     */
    private static void processDownloadUpdate(Context context, List<Record> updateRecords, RecordFileChangeNotify recordFileChangeNotify) throws RecoverableSecurityException {
        DebugUtil.i(TAG, "processDownloadUpdate, updateData=" + ((updateRecords == null) ? null : updateRecords.size()), true);
        if ((updateRecords == null) || updateRecords.isEmpty()) {
            return;
        }
        for (Record record : updateRecords) {
            int count = 0;
            String where = CONCAT_PROJECTION_STRING + "=? and " + COLUMN_NAME_MD5 + " =?";
            String[] selectionArgs = new String[]{record.getConcatRelativePath(), record.getMD5()};
            record.setSyncType(SYNC_TYPE_RECOVERY);
            String newData = generateNewPathForWebRecord(context, record);
            record.setData(newData);
            if (record.fileExistAndCheckSize()) {
                DebugUtil.i(TAG, "processDownloadUpdate file displayname: " + record.getDisplayName() + ", md5: " + record.getMD5() + ", exist and size equal , no need to download", true);
                record.setSyncDownlodStatus(SYNC_STATUS_RECOVERY_FILE_SUC);
                record.setFailedCount(0);
                record.setLastFailedTime(0);
            } else {
                DebugUtil.i(TAG, "processDownloadUpdate file displayname: " + record.getDisplayName() + ", md5: " + record.getMD5() + ", not exist or size not equal , need to download", true);
                record.setSyncDownlodStatus(SYNC_STATUS_RECOVERY_MEGADATA_SUC);
            }
            CloudStaticsUtil.addCloudLog(TAG, "processDownloadUpdate,cloud record "
                    + record.getDisplayName() + ",status=" + record.getSyncDownlodStatus());
            try {
                try {
                    // 本地数据库相同文件路径且md5相同的数据记录
                    List<Record> records = RecorderDBUtil.getRecordData(context, null, where, selectionArgs, null);
                    if ((records != null) && (records.size() > 0)) {
                        String idWhere = COLUMN_NAME_ID + " = ?";
                        for (Record localRecord : records) {
                            if (localRecord != null) {
                                DebugUtil.i(TAG, "processDownloadUpdate  localRecord.getDisplayName" + localRecord.getDisplayName()
                                        + " localRecord.getGlobalId " + record.getGlobalId(), true);
                                String keyId = localRecord.getId() + "";
                                String[] idWhereArgs = new String[]{keyId};

                                if (record.sameGlobalId(localRecord) && record.sameSysVersion(localRecord)) {
                                    DebugUtil.i(TAG, "processDownloadUpdate: same sysVersion : " + record.getSysVersion(), true);
                                    continue;
                                }
                                if (localRecord.isDirty()) {
                                     /*edit mark in local db , (localRecord.getMarkData()!= null)
                                    this condition is incase the first time full recovery after mediadb sync to recordDB,*/
                                    if (!localRecord.isSameBusinessContent(record) && (localRecord.getMarkData() != null)
                                            && (localRecord.getMarkData().length > 0)) {
                                        // 清空本地记录globalId
                                        boolean processSuc = processLocalMarkDirty(context, record, localRecord, recordFileChangeNotify);
                                        CloudStaticsUtil.addCloudLog(TAG, "processDownloadUpdate,local is dirty, mark local record new,"
                                                + localRecord.getDisplayName() + ", result=" + processSuc);
                                    } else {
                                        count = RecorderDBUtil.updateRecordDataWithCloudMark(context, keyId, record.convertToContentValues(),
                                                idWhere, idWhereArgs);
                                        DebugUtil.v(TAG, "processDownloadUpdate, localRecord.isDirty, update count=" + count
                                                + ", where =" + where, true);
                                        CloudStaticsUtil.addCloudLog(TAG, "processDownloadUpdate,localDirty,update cloud db,count=" + count);
                                        continue;
                                    }
                                } else {
                                    count = RecorderDBUtil.updateRecordDataWithCloudMark(context, keyId, record.convertToContentValues(),
                                            idWhere, idWhereArgs);
                                    DebugUtil.v(TAG, "processDownloadUpdate, update count=" + count + ", where =" + where, true);
                                    CloudStaticsUtil.addCloudLog(TAG, "processDownloadUpdate,update cloud record to db,count=" + count);
                                    continue;
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    DebugUtil.e(TAG, "processDownloadUpdate, e=" + e);
                    throw e;
                }
                // find record by uuId, incase that same uuid record insert to db , expose android.database.sqlite.SQLiteConstraintException: UNIQUE constraint failed exception
                if (checkUpdateUUidRecord(context, record, recordFileChangeNotify)) {
                    continue;
                }
                // add new item
                Uri uri = RecorderDBUtil.insertRecordData(context, record.convertToContentValues());
                DebugUtil.v(TAG, "processDownloadUpdate, insert uri=" + uri, true);
                CloudStaticsUtil.addCloudLog(TAG, "processDownloadUpdate,"
                        + "inert cloud record to db " + record.getDisplayName() + ", result =" + (uri != null));
            } catch (Exception e) {
                DebugUtil.e(TAG, "processDownloadUpdate, e=" + e);
                CloudStaticsUtil.addCloudLog(TAG, "processDownloadUpdate, throw error " + e.getMessage());
                throw e;
            }
        }
    }

    /**
     * 查询本地存在相同uuid数据
     * null  return false
     * 1. 本地标记为脏数据(dirty)
     * 1.1 path 不相同 @return false
     * 2.本地标记为非脏数据
     * 2.1 path不相同，md5、size同云端一致 ->rename 本地文件名称为云端名称、更新db
     *
     * @param context
     * @param recordCloud
     * @param recordFileChangeNotify
     * @return
     */
    private static boolean checkUpdateUUidRecord(Context context, Record recordCloud, RecordFileChangeNotify recordFileChangeNotify) {
        int count = 0;
        String uuId = recordCloud.getUuid();
        if (!TextUtils.isEmpty(uuId)) {
            String uuIdWhere = COLUMN_NAME_UUID + " = ?";
            String[] uuIdWhereArg = new String[]{uuId};
            Record recordDb = RecorderDBUtil.getInstance(context).qureyRecordByUUid(uuId);
            DebugUtil.i(TAG, "checkUpdateUUidRecord: uuid: " + uuId + ", record in db: " + recordDb, true);
            if (recordDb != null) {
                if (recordCloud.sameGlobalId(recordDb) && recordCloud.sameSysVersion(recordDb)) {
                    DebugUtil.i(TAG, "checkUpdateUUidRecord: sameSysVersion", true);
                    return true;
                }
                if (recordDb.isDirty()) {
                    if (!recordDb.getConcatRelativePath().equalsIgnoreCase(recordCloud.getConcatRelativePath())) {
                        //uuId equal, path not equal:
                        //1. user has rename the file in local and not sync to cloud imediaty (record db is dirty), the original record from cloud recovered to local
                        CloudSyncRecorderDbUtil.markAsWaitingToUploadForUpdateById(recordDb.getId(), true, true);
                        CloudStaticsUtil.addCloudLog(TAG, "checkUpdateUUidRecord,local is dirty, mark local new data, " + recordDb.getDisplayName());
                        return false;
                    }
                } else {
                    //local record is clean , no change make to local
                    if (!recordDb.getConcatRelativePath().equalsIgnoreCase(recordCloud.getConcatRelativePath())) {
                        //uuId equal, path not equal:
                        //user local data is clean(record db is not dirty) but not match with the newest verison on the cloud
                        DebugUtil.d(TAG, "checkUpdateUUidRecord, local db : " + recordDb.getDisplayName() + ", concatePath: " + recordDb.getConcatRelativePath() + "from cloud: " + recordCloud.getDisplayName() + ", concatedPath: " + recordCloud.getConcatRelativePath() + ", globalid: " + recordCloud.getGlobalId(), true);
                        //not dirty , rename the origin file to the name on the server
                        CloudStaticsUtil.addCloudLog(TAG, "checkUpdateUUidRecord,"
                                + "local not dirty,but different path,local " + recordDb.getConcatRelativePath());
                        if (recordDb.fileExistAndCheckSize() && recordDb.sameFileSize(recordCloud) && recordDb.sameMD5(recordCloud)) {
                            renameLocalFileWhenSameRecord(context, recordDb, recordCloud, recordFileChangeNotify);
                        }
                    }
                    // update the localdb with the newReocord from web ()
                    count = RecorderDBUtil.updateRecordData(context, recordCloud.convertToContentValues(),
                            uuIdWhere, uuIdWhereArg);
                    CloudStaticsUtil.addCloudLog(TAG, "checkUpdateUUidRecord,update cloud record, count=" + count);
                    DebugUtil.d(TAG, "checkUpdateUUidRecord, update record uuid " + uuId + ", update count " + count, true);
                }
                return true;
            } else {
                return false;
            }
        }
        return false;
    }

    /**
     * 本地文件存在，且本地record md5、size同云端一致：
     * 复制文件、重命名本地文件名称，云端数据重命名插入db
     *
     * @param context
     * @param recordCloud
     * @param recordDb
     * @param recordFileChangeNotify
     * @return
     */
    private static boolean processLocalMarkDirty(Context context, Record recordCloud, Record recordDb, RecordFileChangeNotify recordFileChangeNotify) throws RecoverableSecurityException {
        DebugUtil.i(TAG, "processLocalMarkDirty: webRecord: " + recordCloud + "\n " + ", localRecord: " + recordDb, true);
        if (recordDb.fileExistAndCheckSize() && recordDb.sameFileSize(recordCloud) && recordDb.sameMD5(recordCloud)) {
            Uri recordMediaUri = MediaDBUtils.getMediaUriForRecord(recordDb);
            if (recordMediaUri != null) {
                String displayName = recordDb.getDisplayName();
                boolean isFileExists = FileUtils.isFileExist(recordCloud.getRelativePath(), displayName);
                if (isFileExists) {
                    displayName = PathUtil.getNewNameForSyncRecordConflict(recordDb);
                    // 重命名本地文件名称
                    boolean localRenameSuccess = MediaDBUtils.rename(recordMediaUri, displayName) > 0;
                    if (localRenameSuccess) {
                        // 文件重命名成功，更新本地db
                        RecorderDBUtil.getInstance(context).updateDisplayNameByRecordId(String.valueOf(recordDb.getId()), displayName, recordDb.getRecordType(), true);
                        boolean sameGlobalId = recordCloud.sameGlobalId(recordDb);
                        boolean sameUuid = recordCloud.getUuid().equals(recordDb.getUuid());
                        if ((sameGlobalId) || (sameUuid)) {
                            CloudSyncRecorderDbUtil.markAsWaitingToUploadForDeleteById(recordDb.getId(), true, sameGlobalId);
                        }

                        recordMediaUri = MediaDBUtils.getMediaUriForRelativePathAndDisplayName(recordDb.getRelativePath(), displayName);
                    }
                    DebugUtil.i(TAG, "processLocalMarkDirty localRenameSuccess: " + localRenameSuccess, true);
                }

                //判断一下文件名称后缀和mimetype是否匹配，如果不匹配，则需要重置data字段. 避免因文件名称和元数据displayName不一致产生新的记录
                ContentValues contentValues = MediaDBUtils.getCloudContentValues(recordCloud, recordCloud.getDisplayName());
                boolean isFileExtMatched = FileDealUtil.isDisplayNameExtMatchWithMimeType(displayName, recordDb.mMimeType);
                if (!isFileExtMatched) {
                    String destFilePath = PathUtil.getFilePath(recordDb.getRelativePath(), displayName);
                    contentValues.put(MediaStore.Audio.Media.DATA, destFilePath);
                    DebugUtil.i(TAG,  "mimeType not match the file ext, force to set path = " + destFilePath, true);
                }
                Uri destMediaUri = MediaDBUtils.genUri(contentValues);
                if (destMediaUri != null) {
                    boolean copyResult = FileUtils.copyFile(context, recordMediaUri, destMediaUri);
                    if (copyResult) {
                        if (isFileExists) {
                            recordCloud.setSyncDownlodStatus(SYNC_STATUS_RECOVERY_FILE_SUC);
                            recordCloud.setFailedCount(0);
                            recordCloud.setLastFailedTime(0);
                            recordCloud.setSyncType(SYNC_TYPE_RECOVERY);
                            RecorderDBUtil.insertRecordData(context, recordCloud.convertToContentValues());
//                            RecorderDBUtil.getInstance(context).insertOrUpdateNewRecord(
//                                    destMediaUri,
//                                    null,
//                                    markList,
//                                    recordCloud.getRecordType(),
//                                    true);
                        }
                        if (recordFileChangeNotify != null) {
                            int recordType = RecordModeUtil.getRecordTypeForMediaRecord(recordDb);
                            recordFileChangeNotify.setFileChanged(recordType);
                            scanMedia(context, recordCloud.getData(), recordType, recordFileChangeNotify);
                        }
                    } else {
                        DebugUtil.e(TAG, "copy file failed", true);
                        return true;
                    }
                }
            }
        }
        return false;
    }

    @RequiresApi(api = Build.VERSION_CODES.Q)
    private static void processDownloadDelete(Context context, CloudRecoveryRequestSource requestSource, List<Record> deleteRecords, boolean isSyncDelete, RecordFileChangeNotify recordFileChangeNotify) throws Exception {
        DebugUtil.d(TAG, "processDownloadDelete, requestSource: " + requestSource + " deleteData=" + ((deleteRecords == null) ? null : deleteRecords.size()), true);
        if ((deleteRecords == null) || deleteRecords.isEmpty()) {
            return;
        }
        for (Record webRecord : deleteRecords) {
            final String globalId = webRecord.getGlobalId();
            final String relativePath = webRecord.getConcatRelativePath();
            final String md5 = webRecord.getMD5();
            final String uuId = webRecord.getUuid();
            final String fileId = webRecord.getFileId();
            if (TextUtils.isEmpty(globalId)) {
                continue;
            }
            DebugUtil.i(TAG, "processDownloadDelete: delete webrecord relativePath: " + relativePath + ", md5: " + md5 + ", webrecord globalId: " + globalId + ", webrecord fileId: " + webRecord.getFileId(), true);
            Record localRecord = RecorderDBUtil.getInstance(context).getRecordByGlobalId(globalId);
            if (localRecord != null) {
                if (localRecord.isDirty() || requestSource == CloudRecoveryRequestSource.MANUAL) {
                    //is dirty, mark this record waiting to update;
                    // 本地数据有修改，or 开启开关的全量同步，以恢复数据为准，保留本地数据上传云端
                    boolean updateSuc = CloudSyncRecorderDbUtil.markAsWaitingToUploadForDeleteById(localRecord.getId(), true, true);
                    CloudStaticsUtil.addCloudLog(TAG, "processDownloadDelete, delete " + webRecord.getDisplayName()
                            + ",clearLocal result=" + updateSuc + ",reason dirty=" + localRecord.isDirty()
                            + ", requestSource=" + requestSource.getType());
                    DebugUtil.i(TAG, "on Delete on Server: local record dirty localRecord displayName : "
                            + localRecord.getDisplayName() + ", updateGlobalIdAndFileId: " + updateSuc , true);
                } else {
                    Uri uri = MediaDBUtils.getMediaUriForRecord(localRecord);
                    boolean deleteSuccess = CloudSyncRecorderDbUtil.deleteRecordByPath(localRecord.getData(), true);
                    int mediaDeletedcount = 0;
                    CloudStaticsUtil.addCloudLog(TAG, "processDownloadDelete,delete " + webRecord.getDisplayName() + ",result=" + deleteSuccess);
                    if (deleteSuccess && (uri != null)) {
                        try {
                            // 删除文件
                            mediaDeletedcount = MediaDBUtils.delete(uri);
                            DebugUtil.w(TAG, "processDownloadDelete uri : " + uri + ", deleteCount: " + mediaDeletedcount, true);
                        } catch (RecoverableSecurityException e) {
                            DebugUtil.e(TAG, "media delete faile: uri: " + uri, e, true);
                            throw e;
                        }
                        if (recordFileChangeNotify != null) {
                            int recordType = RecordModeUtil.getRecordTypeForMediaRecord(webRecord);
                            recordFileChangeNotify.setFileChanged(recordType);
                        }
                    }
                    if (uri != null) {
                        long recordId = ContentUris.parseId(uri);
                        if (recordId != -1) {
                            ConvertDeleteUtil.deleteConvertData(context, recordId);
                            NoteDbUtils.deleteNoteByMediaId(String.valueOf(recordId));
                        }
                    }
                    DebugUtil.i(TAG, "on Delete on Server: localRecord displayName : " + localRecord.getDisplayName() + ", recordDeleteSuc: " + deleteSuccess + ", uri: " + uri + ", mediaDeleteCount: " + mediaDeletedcount, true);
                }
            } else {
                DebugUtil.e(TAG, "no record find in local or globalId " + globalId + ", delete failed");
            }
        }
    }

//    public static void onServerDeletedForRecovery(Context context, PacketFactory packetFactory, JsonArray jsonArray, boolean isSyncDelete) throws Exception {
//        if ((jsonArray != null) && (jsonArray.size() > 0)) {
//            DebugUtil.i(TAG, " onServerDeletedForRecovery: " + DebugUtil.jsonStringLogGarble(jsonArray.toString()));
//            RecordFileChangeNotify recordFileChangeNotify = new RecordFileChangeNotify();
//            PacketArray<?> packetArray = packetFactory.newKvArray().parse(jsonArray);
//            if ((packetArray != null) && (packetArray.size() > 0)) {
//                final int size = packetArray.size();
//                for (int i = 0; i < size; i++) {
//                    if (CloudSyncAgent.getInstance().issRecordSyncNeedStop()) {
//                        break;
//                    }
//                    final Record webRecord = RecordEntityUtils.toRecord(context, packetArray.get(i));
//                    final String globalId = webRecord.getGlobalId();
//                    final String fileId = webRecord.getFileId();
//                    if (TextUtils.isEmpty(globalId)) {
//                        continue;
//                    }
//                    DebugUtil.i(TAG, "processDownloadDelete: delete , webrecord globalId: " + globalId + ", webrecord fileId: " + webRecord.getFileId());
//                    Record localRecord = RecorderDBUtil.getInstance(context).getRecordByGlobalId(globalId);
//                    if (localRecord != null) {
//                        if (localRecord.isDirty() || ) {
//                            //clear globalId and fileId to upload the file again
//                            //本地存在globalId数据+本地修改过。保留本条数据(清空globalId)
//                            CloudSyncRecorderDbUtil.markAsWaitingToUploadForDeleteById(localRecord.getId(), true);
//                            DebugUtil.i(TAG, "on Delete on Server: local record dirty localRecord displayName : " + localRecord.getDisplayName() + ", isSyncDelete: " + isSyncDelete);
//                        } else {
//                            Uri uri = MediaDBUtils.getMediaUriForRecord(localRecord);
//                            boolean deletesuc = CloudSyncRecorderDbUtil.deleteRecordByPath(localRecord.getData(), true);
//                            int mediaDeletedcount = 0;
//                            if (deletesuc && (uri != null)) {
//                                try {
//                                    mediaDeletedcount = MediaDBUtils.delete(uri);
//                                } catch (RecoverableSecurityException e) {
//                                    DebugUtil.e(TAG, "media delete faile: uri: " + uri, e);
//                                }
//                                int recordType = BaseUtil.INSTANCE.getRecordTypeForMediaRecord(webRecord);
//                                recordFileChangeNotify.setFileChanged(recordType);
//                            }
//                            if (uri != null) {
//                                long recordId = ContentUris.parseId(uri);
//                                if (recordId != -1) {
//                                    ConvertDeleteUtil.deleteConvertData(context, recordId);
//                                }
//                            }
//                            DebugUtil.i(TAG, "on Delete on Server: localRecord displayName : " + localRecord.getDisplayName() + ", recordDeleteSuc: " + deletesuc + ", uri: " + uri + ", mediaDeleteCount: " + mediaDeletedcount);
//                        }
//                    } else {
//                        DebugUtil.e(TAG, "no record find in local: " + globalId + ", delete failed");
//                    }
//                }
//                recordFileChangeNotify.notifyBySendBroadcast(context);
//            }
//        }
//    }

    private static String generateNewPathForWebRecord(Context context, Record record) {
        String outputPath = BaseUtil.INSTANCE.getPhoneStorageDir(context) + File.separator + record.getConcatRelativePath();
        return outputPath;
    }

    //rename the local file when local and cloud are the same record file
    //if RecoverableSecurityException throw to onMetaDataRecoveryEnd return cloud
    private static void renameLocalFileWhenSameRecord(Context context, Record recordDb, Record recordCloud, RecordFileChangeNotify recordFileChangeNotify) throws RecoverableSecurityException {
        Uri recordMediaUri = MediaDBUtils.getMediaUriForRecord(recordDb);
        if (recordMediaUri != null) {
            // 本地已经有同云端相同文件,
            if (recordCloud.fileExist()) {
                Record dbSamePathRecord = RecorderDBUtil.getInstance(context).getRecordByRelativePathAndDisplayName(recordCloud.getRelativePath(), recordCloud.getDisplayName());
                DebugUtil.i(TAG, "renameLocalFileWhenSameRecord<< recordCloud.fileExist(),query local db:" + dbSamePathRecord, true);
                if (dbSamePathRecord != null) {
                    Uri sameCloudPathUri = MediaDBUtils.getMediaUriForRecord(dbSamePathRecord);
                    if (sameCloudPathUri != null) {
                        String newName = PathUtil.getNewNameForSyncRecordConflict(dbSamePathRecord);
                        boolean localRenameSuccess = MediaDBUtils.rename(sameCloudPathUri, newName) > 0;
                        DebugUtil.e(TAG, "renameLocalFileWhenSameRecord dbSamePathRecord localRenameSuccess " + localRenameSuccess + ",newName: " + newName + "  " + dbSamePathRecord);
                        if (localRenameSuccess) {
                            RecorderDBUtil.getInstance(context).updateDisplayNameByRecordId(String.valueOf(dbSamePathRecord.getId()), newName, dbSamePathRecord.getRecordType(), true);
                        }
                        CloudStaticsUtil.addCloudLog(TAG, "renameLocalFileWhenSameRecord, dbSamePathRecord renameResult " + localRenameSuccess
                                + ",newName: " + newName + " dbname:" + dbSamePathRecord.getDisplayName());
                    }
                }
            }
            int renameResult = MediaDBUtils.rename(recordMediaUri, recordCloud);
            if (renameResult > 0) {
                CloudSyncRecorderDbUtil.updateDisplayNameForRecoveryRename(true, recordDb.getGlobalId(), recordCloud);
                if (recordFileChangeNotify != null) {
                    int recordType = RecordModeUtil.getRecordTypeForMediaRecord(recordDb);
                    recordFileChangeNotify.setFileChanged(recordType);
                }
                DebugUtil.i(TAG, "rename when local and cloud are the same record file : " + recordDb.getDisplayName() + " ----> " + recordCloud.getDisplayName(), true);
                CloudStaticsUtil.addCloudLog(TAG, "renameLocalFileWhenSameRecord, dbName="
                        + recordDb.getDisplayName() + "cloudName=" + recordCloud.getDisplayName());
            }
        }
    }

    private static GroupInfo toGroupInfo(Gson gson, CloudMetaDataRecord cloudMetaDataRecord) {
        try {
            CloudGroupInfoField fromJson = gson.fromJson(cloudMetaDataRecord.getFields(), CloudGroupInfoField.class);
            if (fromJson == null) {
                return null;
            }
            GroupInfo groupInfo = fromJson.toGroupInfo();
            groupInfo.setSysVersion(cloudMetaDataRecord.getSysVersion());
            groupInfo.setMGroupGlobalId(cloudMetaDataRecord.getSysRecordId());
            return groupInfo;
        } catch (Exception e) {
            DebugUtil.e(TAG, "to groupInfo error " + e + "\n cloudMetaDataRecord: " + cloudMetaDataRecord);
            CloudStaticsUtil.addCloudLog(TAG, "toGroupInfo,error,data=" + cloudMetaDataRecord.getFields() + ",error=" + e.getMessage());
            return null;
        }
    }

    private static Record toRecord(Gson gson, CloudMetaDataRecord cloudMetaDataRecord) {
        try {
            CloudRecordField fromJson = gson.fromJson(cloudMetaDataRecord.getFields(), CloudRecordField.class);
            if (fromJson == null) {
                return null;
            }
            Record record = fromJson.toRecord();
            record.setGlobalId(cloudMetaDataRecord.getSysRecordId());
            record.setSysVersion(cloudMetaDataRecord.getSysVersion());
            List<CloudMetaDataFileInfo> fileInfos = cloudMetaDataRecord.getFileInfos();
            if ((fileInfos != null) && (fileInfos.size() > 0)) {
                record.setFileId(fileInfos.get(0).getOcloudId());
            }
            //云同步后，需要更新groupId
            if (record.getGroupId() == -1) {
                int groupId = GroupInfoDbUtil.getGroupIdByGroupUUID(BaseApplication.getAppContext(), record.getGroupUuid());
                if (groupId != -1) {
                    record.setGroupId(groupId);
                }
            }
            if (record.getGroupUuid() == null) {
                GroupInfoManager.getInstance(BaseApplication.getAppContext()).resetGroupInfoForRecord(record);
            }

            return record;
        } catch (Exception e) {
            DebugUtil.e(TAG, "to record error " + e + "\n cloudMetaDataRecord: " + cloudMetaDataRecord);
            CloudStaticsUtil.addCloudLog(TAG, "toRecord,error,data=" + cloudMetaDataRecord.getFields() + ",error=" + e.getMessage());
            return null;
        }
    }

    private static void scanMedia(Context context, String path, int recordType, RecordFileChangeNotify fileChangeNotify) {
        MediaDataScanner.getInstance().mediaScanWithCallback(context, new String[]{path}, (path1, uri) -> {
            fileChangeNotify.setFileChanged(recordType);
            fileChangeNotify.notifyBySendBroadcast(context);
        });
    }
}
