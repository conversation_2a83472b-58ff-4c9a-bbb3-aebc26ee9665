package com.recorder.cloudkit.sync.config

import android.content.Context
import com.heytap.cloudkit.libcommon.utils.CloudDeviceInfoUtil
import com.recorder.cloudkit.BuildConfig
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.PrefUtil
import java.lang.Exception
import java.util.Calendar
import java.util.Locale

object CloudConfigUtil {
    private const val TAG = "CloudConfigUtil"

    private const val KEY_CONFIG_H5 = "record.ocloudH5"
    private const val DATA_TYPE_H5 = "h5"

    private const val KEY_CONFIG_QUICK_APP = "record.ocloudQuickApp"
    private const val DATA_TYPE_QUICK_APP = "quickapp"

    private const val REGION_DEFAULT_ALL_EXPORT = "ALL"

    /*SP中云端公共配置项所有值以及获取更新时间Key*/
    private const val SP_KEY_CLOUD_CONFIG = "sp_key_cloud_config"
    private const val SP_KEY_CLOUD_CONFIG_TIME_MILLS = "sp_key_cloud_config_update_time"

    /*公共配置本地缓存有效时间，3天*/
    private const val DAY_OF_CONFIG_PERIOD = 3

    /**
     * 检查是否需要从云端拉取云同步配置信息
     * 3天拉一次
     */
    @JvmStatic
    fun checkNeedFetchCloudConfig(): Boolean {
        val ignoreTime = PrefUtil.getLong(BaseApplication.getAppContext(), SP_KEY_CLOUD_CONFIG_TIME_MILLS, 0)
        if (ignoreTime <= 0) {
            return true
        }
        val now = Calendar.getInstance()
        val threeDayLater = Calendar.getInstance()
        threeDayLater.timeInMillis = ignoreTime
        threeDayLater.add(Calendar.DAY_OF_WEEK, DAY_OF_CONFIG_PERIOD)
        DebugUtil.i(TAG, "checkNeedFetchCloudConfig  ignoreTime$ignoreTime,threeMonthsLater.timeInMillis${threeDayLater.timeInMillis}", true)
        return now.timeInMillis > threeDayLater.timeInMillis
    }

    /**
     * 外销跳转webH5
     */
    @JvmStatic
    fun getCloudAppH5Link(context: Context): String? {
        return getCloudAppH5LinkFromCache(context.applicationContext, KEY_CONFIG_H5, DATA_TYPE_H5)
    }

    /**
     * 内销跳转快应用
     */
    @JvmStatic
    fun getCloudQuickAppLink(context: Context): String? {
        return getCloudAppH5LinkFromCache(context.applicationContext, KEY_CONFIG_QUICK_APP, DATA_TYPE_QUICK_APP)
    }

    /**
     * 从配置项获取快应用或者web 跳转地址
     * @param context
     * @param key
     * @see KEY_CONFIG_H5  h5
     * @see KEY_CONFIG_QUICK_APP  快应用
     * @param type
     * @see DATA_TYPE_H5   h5
     * @see  DATA_TYPE_QUICK_APP 快应用
     */
    @JvmStatic
    private fun getCloudAppH5LinkFromCache(context: Context, key: String, type: String): String? {
        val currentRegion = CloudDeviceInfoUtil.getDeviceRegionMark(context)
        val brand = BuildConfig.FLAVOR_B.uppercase()
        val h5LinkList = getCloudConfigFromCache(context).filter {
            // 匹配品牌、类型
            (it.key == key) && (it.dataBean?.type == type) && (it.brand?.contains(brand) ?: false)
        }
        DebugUtil.i(TAG, "currentRegion:$currentRegion,brand is $brand,h5LinkList is $h5LinkList ", true)

        if (h5LinkList.isNullOrEmpty().not()) {
            var h5LinkUrl = h5LinkList.filter {
                (it.region?.contains(currentRegion.lowercase(Locale.getDefault())) ?: false) ||
                        (it.region?.contains(currentRegion.uppercase(Locale.getDefault())) ?: false)
            }.getOrNull(0)?.dataBean?.linkUrl

            if (h5LinkUrl.isNullOrBlank()) {
                h5LinkUrl = h5LinkList.filter {
                    (it.region?.contains(REGION_DEFAULT_ALL_EXPORT.lowercase(Locale.getDefault())) ?: false)
                            || (it.region?.contains(REGION_DEFAULT_ALL_EXPORT.uppercase(Locale.getDefault())) ?: false)
                }.getOrNull(0)?.dataBean?.linkUrl
            }
            return h5LinkUrl
        }
        return null
    }

    /**
     * 从缓存中获取配置信息-云同步H5地址
     */
    @JvmStatic
    fun getCloudConfigFromCache(context: Context): List<CloudConfigBean> {
        val defaultData = emptyList<CloudConfigBean>()
        return (PrefUtil.getObject(context, SP_KEY_CLOUD_CONFIG, defaultData) ?: defaultData) as List<CloudConfigBean>
    }

    @JvmStatic
    fun saveCloudConfigToCache(context: Context, data: List<CloudConfigBean>) {
        DebugUtil.i(TAG, "saveCloudConfigToCache $data ", true)
        try {
            if (PrefUtil.putObject(context, SP_KEY_CLOUD_CONFIG, data)) {
                PrefUtil.putLong(context, SP_KEY_CLOUD_CONFIG_TIME_MILLS, System.currentTimeMillis())
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "saveCloudConfigToCache error $e")
        }
    }
}