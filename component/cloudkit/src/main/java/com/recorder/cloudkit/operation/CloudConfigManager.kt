/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - CloudConfigManager.kt
 ** Description:
 ** Version: 1.0
 * Date: 2024/9/18
 * Author: 80266877
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  80266877       2024/9/18     1.0     create file
 ****************************************************************/
package com.recorder.cloudkit.operation

import androidx.lifecycle.MutableLiveData
import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.heytap.cloudkit.libsync.cloudswitch.bean.SwitchState
import com.heytap.cloudkit.libsync.ext.CloudOperationManager
import com.heytap.cloudkit.libsync.ext.CloudOperationManager.ICloudOperationsCallback
import com.heytap.cloudkit.libsync.ext.CloudSyncManager
import com.recorder.cloudkit.sync.CloudSynStateHelper
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.StorageUtil
import com.soundrecorder.modulerouter.cloudkit.CloudSwitchState
import org.json.JSONObject

object CloudConfigManager {
    interface CloudConfigResult {
        fun success(data: CloudOperationResponseData)
        fun onFail()
    }



    private const val STORAGE_SPACE = "localRemainStorageSpace"
    private const val AUTO_SYNC = "swAutoSync"
    private const val MOBILE_DATA_ALLOW = "swMobileDataAllow"

    private const val TAG = "CloudConfigManager"

    private const val MODE = "recordData"
    private const val REQUEST_PATH = "record_top"
    private const val KEY_AUTOMATIC_SYN = "automatic_syn"
    private const val KEY_ALLOW_MOBILE_DATA = "allow_data"

    /**
     * 获取云服务 顶部提示配置
     */
    @JvmStatic
    fun getOperationData(
        dataStr: MutableLiveData<String>
    ) {

        kotlin.runCatching {
            CloudSyncManager.getInstance().setEnableCtaRequest(true) //调用获取配置接口以前，必须设置enable
//            CloudSyncManager.getInstance().startCloudSyncService() //注释掉此行代码,也能获取到数据
            val params = JSONObject()
            val freeDiskSpace = StorageUtil.getFreeDiskSpace()
            var autoSync = false
            var allowMobileData = false
            val switchState = CloudSynStateHelper.getSwitchState(true) //检查登陆状态，然后获取开关状态
            when (switchState) {
                SwitchState.CLOSE.state,
                CloudSwitchState.NOT_LOGIN -> {
                    autoSync = false
                    allowMobileData = false
                }

                SwitchState.OPEN_ALL.state -> {
                    autoSync = true
                    allowMobileData = true
                }

                SwitchState.OPEN_ONLY_WIFI.state -> {
                    autoSync = true
                    allowMobileData = false
                }
            }
            params.put(STORAGE_SPACE, freeDiskSpace)
            params.put(AUTO_SYNC, autoSync)
            params.put(MOBILE_DATA_ALLOW, allowMobileData)
            val requestJson = params.toString()
            DebugUtil.d(TAG, "getCloudSettings params = $requestJson", true)
            CloudOperationManager.getInstance().getOperationData(
                MODE, REQUEST_PATH, requestJson,
                object : ICloudOperationsCallback {
                    override fun onSuccess(operations: String?) {
                        DebugUtil.d(TAG, "onSuccess:$operations")
                        operations?.let { result ->
                            if (result.isNotEmpty()) {
                                kotlin.runCatching {
                                    dataStr.postValue(operations)
                                }.onFailure {
                                    DebugUtil.e(TAG, "onSuccess but error ${it.message}")
                                }
                            }
                        }
                    }

                    override fun onError(requestPath: String, error: CloudKitError) {
                        DebugUtil.w(TAG, "requestPath = $requestPath, onError: $error")
                    }
                })
        }.onFailure {
            DebugUtil.e(TAG, "getOperationData error,  ${it.message}")
        }
    }
}
