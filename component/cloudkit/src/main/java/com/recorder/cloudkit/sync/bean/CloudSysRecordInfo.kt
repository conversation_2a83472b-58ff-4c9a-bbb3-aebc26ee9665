package com.recorder.cloudkit.sync.bean

/**
 *  sysRecordInfo='{"sysCreateTime":1654086496707,"sysCreatedDeviceId":"861507049856596","sysDataType":2,"sysFiles":[{"ocloudId":"oc202206010291227038_2086901062","size":51920}],
"sysOldVersion":0,"sysProtocolVersion":0,"sysRecordId":"6707ec10-6017-4114-99d0-6fe471ad6841","sysRecordType":"item_info","sysSize":51920,
"sysStatus":0,"sysUniqueId":"4b61cb324b846f5af3058eb831113f64",
"sysUpdateDeviceId":"861507049856596","sysUpdateTime":1655259828466,"sysUserId":"2086901062","sysVersion":111837537736251904}',
 */
data class CloudSysRecordInfo(var sysRecordId: String) {
    var sysDataType: Int = 2
    var sysUniqueId: String? = null
    var sysVersion: Long = 0
    var sysRecordType: String = ""
    var sysFiles: List<SysFilesItem>? = null
    var sysStatus: Int = 0


    data class SysFilesItem(var ocloudId: String?, var size: String?)
}